/** @type {import('ts-jest').JestConfigWithTsJest} **/
module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  roots: ["<rootDir>/stacks"],
  testMatch: [
    "**/stacks/**/tests/**/*.ts",
  ],
  transform: {
    "^.+\\.tsx?$": "ts-jest",
  },
  collectCoverage: true,
  coverageDirectory: "coverage",
  coverageReporters: ["text", "text-summary", "lcov", "html"],
  coverageThreshold: {
    global: {
      lines: 80,
      branches: 70,
      functions: 80,
      statements: 80,
    },
  },
  testTimeout: 10000,
  verbose: false,
  collectCoverageFrom: [
    "<rootDir>/stacks/**/src/**/*.{js,jsx,ts,tsx}",
  ],
  testPathIgnorePatterns: [
    "<rootDir>/stacks/share-resources/src/interfaces/*.{ts}",
  ],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/stacks/$1",
  },
};