WITH
    WO_INIT AS (
        SELECT
            WO.WO,
            WO.AC,
            WO.AC_TYPE,
            WO.PROJECT,
            WO.EXTERNAL_REFERENCE,
            WO.WO_CATEGORY,
            WO.PRIORITY,
            WO.STATUS,
            WO.LOCATION,
            WO.VENDOR,
            WO.SITE,
            REPLACE (REPLACE (WO.WO_DESCRIPTION, '"', ' '), '&', 'and') AS "WO_DESCRIPTION",
            WO.SCHEDULE_ORG_COMPLETION_DATE,
            WO.SCHEDULE_ORG_COMPLETION_HOUR,
            WO.SCHEDULE_ORG_COMPLETION_MINUTE,
            WO.SCHEDULE_COMPLETION_DATE,
            WO.SCHEDULE_COMPLETION_HOUR,
            WO.SCHEDULE_COMPLETION_MINUTE,
            WO.SCHEDULE_START_DATE,
            WO.SCHEDULE_START_HOUR,
            WO.SCHEDULE_START_MINUTE,
            WO.ACTUAL_START_DATE,
            WO.ACTUAL_START_HOUR,
            WO.ACTUAL_START_MINUTE,
            WO.CREATED_DATE,
            WO.CREATED_BY,
            WO.MODIFIED_DATE,
            WO.MODIFIED_BY
        FROM
            ODB.WO "WO"
        WHERE
            SUBSTR (WO.LOCATION, 4, 4) IN ('1', '2', '3', '4', '5', '6', '7', '8', '9')
            AND WO.STATUS IN ('CLOSED', 'COMPLETED', 'POSTCOMPLT', 'OPEN')
            AND (
                WO.CREATED_BY = 'TRAXIFACE'
                OR WO.PROJECT = 'EMS'
            )
            AND WO.SCHEDULE_COMPLETION_DATE BETWEEN SYSDATE - 365 AND SYSDATE  + 730
    ),
    WO_INFO AS (
        SELECT
            WO.WO,
            WO.AC,
            CASE
                WHEN WO.PROJECT = 'EMS'
                AND WO.EXTERNAL_REFERENCE IS NULL THEN WO.WO || '_' || WO.AC
                ELSE TRIM(SUBSTR (WO.EXTERNAL_REFERENCE, 1, 10))
            END AS "EVENT_ID",
            CASE
                WHEN WO.PROJECT = 'EMS' THEN 'EMS'
                ELSE (
                    CASE
                        WHEN WO.WO_CATEGORY = 'HMV'
                        AND WO.PRIORITY = 'LOW' THEN 'HML'
                        ELSE (
                            CASE
                                WHEN WO.WO_CATEGORY IN ('OOS', 'PRK') THEN WO.WO_CATEGORY
                            END
                        )
                    END
                )
            END AS "EVENT_TYPE",
            CASE
                WHEN WO.STATUS IN ('OPEN', 'COMPLETED') THEN TO_CHAR (WO.SCHEDULE_ORG_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                    SUBSTR (
                        LPAD (WO.SCHEDULE_ORG_COMPLETION_HOUR, 2, '0') || ':' || LPAD (WO.SCHEDULE_ORG_COMPLETION_MINUTE, 2, '0'),
                        1,
                        8
                    )
                )
                ELSE TO_CHAR (WO.SCHEDULE_COMPLETION_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                    SUBSTR (
                        LPAD (WO.SCHEDULE_COMPLETION_HOUR, 2, '0') || ':' || LPAD (WO.SCHEDULE_COMPLETION_MINUTE, 2, '0'),
                        1,
                        8
                    )
                )
            END AS "ACTUAL_COMPLETION_DATETIME",
            WO.SCHEDULE_START_DATE,
            TO_CHAR (WO.ACTUAL_START_DATE, 'YYYY-MM-DD') || ' ' || TRIM(
                SUBSTR (
                    LPAD (WO.ACTUAL_START_HOUR, 2, '0') || ':' || LPAD (WO.ACTUAL_START_MINUTE, 2, '0'),
                    1,
                    8
                )
            ) AS "ACTUAL_START_DATETIME"
        FROM
            WO_INIT "WO"
    ),
    TASK_COUNTS AS (
        SELECT
            WOTC.WO,
            COUNT(
                CASE
                    WHEN NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "TOTAL_TC",
            COUNT(
                CASE
                    WHEN WOTC.STATUS = 'CANCEL'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "TOTAL_CANCEL_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "TOTAL_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'OPEN'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "OPEN_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'CLOSED'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "CLOSED_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'CANCEL'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "CANCEL_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "TOTAL_NON_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'OPEN'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "OPEN_NON_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'CLOSED'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "CLOSED_NON_ROUTINE_TC",
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'CANCEL'
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS "CANCEL_NON_ROUTINE_TC",
            SUM(
                CASE
                    WHEN WOTC.STATUS IN ('CLOSED', 'CANCEL')
                    AND NVL (WOTC.TYPE, 'N/A') <> 'CA' THEN 1
                    ELSE 0
                END
            ) AS "COMPLETED_OR_CANCELED_TC"
        FROM
            ODB.WO_TASK_CARD WOTC
        WHERE
            WOTC.WO IN (
                SELECT
                    WO
                FROM
                    WO_INFO
            )
        GROUP BY
            WOTC.WO
    ),
    MAN_HOURS AS (
        SELECT
            WOTC.WO,
            NVL (
                SUM(
                    CASE
                        WHEN VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                    END
                ),
                0
            ) AS "TOTAL_MH",
            NVL (
                SUM(
                    CASE
                        WHEN WOTC.STATUS <> 'OPEN'
                        AND VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                    END
                ),
                0
            ) AS "TOTAL_MH_COMPLETED"
        FROM
            ODB.WO_TASK_CARD WOTC
            INNER JOIN ODB.VENDOR_CONTRACT_TASK VCT ON WOTC.TASK_CARD = VCT.TASK_CARD
        WHERE
            WOTC.WO IN (
                SELECT
                    WO
                FROM
                    WO_INFO
            )
        GROUP BY
            WOTC.WO
    ),
    LATEST_FLIGHT AS (
        SELECT
            AF.AC,
            AF.FLIGHT,
            AF.ORIGIN,
            AF.DESTINATION,
            AF.FLIGHT_DATE,
            AF.ON_HOUR,
            AF.ON_MINUTE
        FROM
            ODB.AC_ACTUAL_FLIGHTS AF
        WHERE
            (AF.AC, AF.CREATED_DATE) IN (
                SELECT
                    AC,
                    MAX(CREATED_DATE)
                FROM
                    ODB.AC_ACTUAL_FLIGHTS
                WHERE
                    AC IN (
                        SELECT
                            AC
                        FROM
                            WO_INFO
                    )
                GROUP BY
                    AC
            )
    )
SELECT
    WO_INFO.WO,
    WO_INFO.AC,
    WO_INIT.AC_TYPE,
    WO_INFO.EVENT_ID,
    WO_INIT.LOCATION,
    WO_INIT.WO_DESCRIPTION,
    WO_INIT.VENDOR,
    WO_INIT.SITE,
    WO_INIT.PROJECT,
    WO_INIT.WO_CATEGORY,
    WO_INIT.PRIORITY,
    WO_INIT.EXTERNAL_REFERENCE,
    WO_INIT.STATUS,
    TO_DATE (
        WO_INFO.ACTUAL_COMPLETION_DATETIME,
        'YYYY-MM-DD HH24:MI:SS'
    ) - TO_DATE (
        WO_INFO.ACTUAL_START_DATETIME,
        'YYYY-MM-DD HH24:MI:SS'
    ) AS "DURATION",
    CASE
        WHEN TC.TOTAL_TC = 0
        OR TC.COMPLETED_OR_CANCELED_TC = 0 THEN 0
        WHEN WO_INFO.SCHEDULE_START_DATE > SYSDATE THEN 0
        ELSE ROUND(TC.COMPLETED_OR_CANCELED_TC / TC.TOTAL_TC * 100)
    END AS "PERCENT_COMPLETED",
    TC.TOTAL_TC,
    TC.TOTAL_CANCEL_TC,
    TC.TOTAL_ROUTINE_TC,
    TC.OPEN_ROUTINE_TC,
    TC.CLOSED_ROUTINE_TC,
    TC.CANCEL_ROUTINE_TC,
    TC.TOTAL_NON_ROUTINE_TC,
    TC.OPEN_NON_ROUTINE_TC,
    TC.CLOSED_NON_ROUTINE_TC,
    TC.CANCEL_NON_ROUTINE_TC,
    MH.TOTAL_MH,
    MH.TOTAL_MH_COMPLETED,
    CASE
        WHEN MH.TOTAL_MH = 0 THEN 0
        ELSE ROUND(MH.TOTAL_MH_COMPLETED / MH.TOTAL_MH * 100)
    END AS "MH_ROUTINE_CHECK_COMPLETION",
    AC.AC_TYPE AS "TYPE",
    AC.AC_SN,
    AC.AC_FLIGHT_HOURS,
    AC.AC_FLIGHT_MINUTES,
    AC.AC_CYCLES,
    AC.LAST_AC_REGISTRATION,
    SUBSTR (AC.BASIC_NUMBER, 1, 2) AS "OPERATOR",
    LF.FLIGHT,
    LF.ORIGIN,
    LF.DESTINATION,
    LF.FLIGHT_DATE,
    LF.ON_HOUR,
    LF.ON_MINUTE,
    WO_INIT.SCHEDULE_START_DATE,
    WO_INIT.SCHEDULE_START_HOUR,
    WO_INIT.SCHEDULE_START_MINUTE,
    WO_INIT.SCHEDULE_COMPLETION_DATE,
    WO_INIT.SCHEDULE_COMPLETION_HOUR,
    WO_INIT.SCHEDULE_COMPLETION_MINUTE,
    WO_INIT.SCHEDULE_ORG_COMPLETION_DATE,
    WO_INIT.SCHEDULE_ORG_COMPLETION_HOUR,
    WO_INIT.SCHEDULE_ORG_COMPLETION_MINUTE,
    WO_INIT.ACTUAL_START_DATE,
    WO_INIT.ACTUAL_START_HOUR,
    WO_INIT.ACTUAL_START_MINUTE,
    WO_INIT.CREATED_DATE,
    WO_INIT.CREATED_BY,
    WO_INIT.MODIFIED_DATE,
    WO_INIT.MODIFIED_BY,
    LM.TIME_ZONE_NAME
FROM
    WO_INFO WO_INFO
    LEFT JOIN WO_INIT WO_INIT ON WO_INFO.WO = WO_INIT.WO
    LEFT JOIN TASK_COUNTS TC ON WO_INFO.WO = TC.WO
    LEFT JOIN MAN_HOURS MH ON WO_INFO.WO = MH.WO
    LEFT JOIN ODB.AC_MASTER AC ON WO_INFO.AC = AC.AC
    LEFT JOIN LATEST_FLIGHT LF ON WO_INFO.AC = LF.AC
    LEFT JOIN ODB.LOCATION_MASTER LM ON LM.LOCATION = WO_INIT.LOCATION
ORDER BY
    WO_INFO.SCHEDULE_START_DATE;