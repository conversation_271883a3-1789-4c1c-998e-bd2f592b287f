const knex = require('knex');
const { asyncForEach, asyncMapRunInParallelInChunks } = require('../async')
const path = require('path');

class QueryManager {
  constructor(params) {
    if (params)
      this.init(params)
  }

  initOnce = (params) => {
    if (!this.knex)
      this.init(params);
  }

  init = ({
    host,
    user,
    password,
    database,
    port = 3306,
    debug = false,
    asyncStackTraces = false,
    pool = {},
    postProcessResponse = null,
    acquireConnectionTimeout = 1000, // Milliseconds
    client = 'mysql2',
    hooks = {}, // query, query-success, query-error, error,
    migrations = null // {directory: "./migrations", tableName:"knex_migrations", ...}
  }) => {
    this.host = host;
    this.user = user;
    this.password = password;
    this.database = database;
    this.port = port;
    this.debug = debug;
    this.asyncStackTraces = asyncStackTraces;
    this.pool = {
      min: 0,
      max: 1,
      idleTimeoutMillis: 60000, // free resouces are destroyed after this many milliseconds (1 minute by default)
      afterCreate: function (conn, done) {
        logMessage({
          message: 'QueryManager - CONNECTION CREATED',
          debug
        })
        done(null, conn);
      }, ...pool
    };
    this.postProcessResponse = postProcessResponse;
    this.client = client;
    this.acquireConnectionTimeout = acquireConnectionTimeout;
    this.migrations = migrations;

    this.statements = [];

    this.getNewKnexInstance();

    const hooksKeys = Object.keys(hooks);

    // query, query-success, query-error, error
    if (hooksKeys) {
      hooksKeys.forEach(key => this.knex.on(key, hooks[key]));
    }
  }

  getNewKnexInstance = _ => this.knex = knex({
    client: this.client,
    debug: this.debug,
    asyncStackTraces: this.asyncStackTraces,
    postProcessResponse: this.postProcessResponse,
    connection: {
      host: this.host,
      port: this.port,
      user: this.user,
      password: this.password,
      database: this.database
    },
    acquireConnectionTimeout: this.acquireConnectionTimeout,
    pool: this.pool,
    migrations: this.migrations
  })

  isConnected = async _ => {
    try {
      return !! await this.getKnex().raw('SELECT 1');
    } catch (error) {
      //console.log(error)
      return false;
    }
  }
  reConnect = async _ => this.getNewKnexInstance()
  destroy = async _ => this.getBuilder().destroy();

  addRawStatements = (rawStatements) => {
    const statementsToAdd = Array.isArray(rawStatements) ? rawStatements : [rawStatements];

    statementsToAdd.forEach(statement => {
      if (typeof statement !== 'string') {
        throw new Error(`${statement} is not a raw statement`);
      }

      this.statements.push(statement);

      logMessage({
        message: 'QueryManager - Statement added',
        params: {
          statement: statement,
          method: 'addRawStatements'
        },
        debug: this.debug
      })
    })
  }

  addStatements = (callbackOrStatements) => {
    let statements = callbackOrStatements;

    if (typeof callbackOrStatements === 'function') {
      statements = callbackOrStatements(this.getKnex());
    }

    const statementsToAdd = Array.isArray(statements) ? statements : [statements];

    statementsToAdd.forEach(statement => {
      this.addRawStatements(statement.toString());

      logMessage({
        message: 'QueryManager - Statement added',
        params: {
          statement: statement.toString(),
          method: 'addStatements'
        },
        debug: this.debug
      })
    })
  }

  clearStatements = _ => this.statements = []
  getStatements = _ => this.statements
  getKnex = _ => this.knex
  getBuilder = _ => this.getKnex()
  getMigrate = _ => this.knex.migrate

  runStatements = async ({ retryAttempts = 3, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      debug: this.debug,
      attempts: retryAttempts,
      retriesDelay,
      callback: () => this.transaction(async (trx) => {
        return await asyncForEach(this.statements, async statement => {
          return await trx.raw(statement);
        })
      })
    })
    this.clearStatements();

    return response;
  }

  runStatementsInParallel = async ({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      debug: this.debug,
      attempts: retryAttempts,
      retriesDelay,
      callback: () => this.transaction(async (trx) => {
        return await asyncMapRunInParallelInChunks(this.statements, batchSize, async statement => {
          return await trx.raw(statement);
        })
      })
    })
    this.clearStatements();

    return response;
  }

  runStatementsWithoutTransaction = async ({ retryAttempts = 3, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      debug: this.debug,
      attempts: retryAttempts,
      retriesDelay,
      callback: () => asyncForEach(this.statements, async statement => {
        return await this.getKnex().raw(statement);
      })
    })
    this.clearStatements();

    return response;
  }

  runStatementsWithoutTransactionInParallel = async ({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction } = {}) => {
    const response = await retrier({
      debug: this.debug,
      attempts: retryAttempts,
      retriesDelay,
      callback: () => asyncMapRunInParallelInChunks(this.statements, batchSize, async statement => {
        return await this.getKnex().raw(statement);
      })
    })
    this.clearStatements();

    return response;
  }

  execute = async callback => await callback(this.getKnex())

  transaction = async (callback) => {
    const trx = await this.getKnex().transaction();

    if (!callback)
      return trx;

    try {
      const response = await callback(trx);
      await trx.commit();
      return response;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  listPendingMigrations = async () => {
    const builder = this.getBuilder();

    // Index 0 -> already migrated
    // Index 1 -> To be migrated
    const list = await builder.migrate.list();
    const toBeMigrated = [];

    list[1].forEach(({ file, directory }) => {
      const absoluteDir = path.resolve(process.cwd(), `${directory}`);
      const { up, down } = require(`${absoluteDir}/${file}`);

      toBeMigrated.push({
        file,
        up: up(builder).toString(),
        down: down(builder).toString()
      })

    });

    return toBeMigrated;
  }

  runMigrations = async () => {
    const builder = this.getBuilder();

    try {
      const migrationResponse = await builder.migrate.latest();
      const migrationStatusResponse = await builder.migrate.status();

      logMessage({
        message: 'QueryManager - run migrations',
        params: {
          response: migrationResponse,
          status: migrationStatusResponse,
          method: 'runMigrations'
        },
        debug: this.debug
      })

      if (migrationStatusResponse !== 0) {
        throw new Error(`Migration status returned ${migrationStatusResponse}`);
      }

      logMessage({
        message: 'QueryManager - Migrations completed successfully',
        params: {
          method: 'runMigrations'
        },
        debug: this.debug
      })

      return { status: migrationStatusResponse, success: true, message: "Migrations completed successfully" }
    } catch (error) {

      logMessage({
        message: "QueryManager - Performing a rollback due to error: " + error,
        params: {
          method: 'runMigrations'
        },
        debug: this.debug
      })

      await builder.migrate.rollback();

      throw error;
    }
  }
}

const logMessage = ({ message, params = {}, debug = false }) => {
  if (!debug) {
    return
  }

  const logMsg = Object.assign({}, params);
  logMsg.message = message;

  console.log(JSON.stringify(logMsg));
}

const retrier = async ({ callback, attempts = 3, retriesDelay = retriesDelayFunction, debug = false }) => {
  let retry = 0;

  try {
    while (attempts > retry) {
      try {
        const callbackResponse = await callback();

        return callbackResponse;
      } catch (err) {
        if (debug)
          logMessage({
            message: 'QueryManager - Retrying',
            params: {
              currentAttempt: retry,
              totalAttempt: attempts,
            },
            debug
          })

        await sleep(retriesDelay(retry));

        retry++;
        if (attempts == retry) {
          throw err;
        }
      }
    }
  } catch (err) {
    logMessage({
      message: 'QueryManager - Failed after tries',
      params: {
        currentAttempt: retry,
        totalAttempt: attempts,
      },
      debug
    })

    throw err;
  }
}

const retriesDelayFunction = (retry) => 1000 * (retry / 2);

const sleep = (n) => {
  return new Promise(function (resolve) {
    setTimeout(resolve, n);
  });
}
module.exports = QueryManager;