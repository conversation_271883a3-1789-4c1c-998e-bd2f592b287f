# Query Manager
Utility that helps developers interact with the database. It leverages the [Knex.js](https://knexjs.org/guide/) library.

Main Features:
- Query Builder.
- DB Migrations.
- Human-friendly API.
- Retries Mechanism on DB operations.
- All or nothing DB operations (Transactions).

## Installation
Add this module to your project's package.json dependencies section:
```json
"dependencies": {
    "ac-utils": "git+ssh://*****************/aircanada-m3/ac-utils.git#RELEASE_TAG"
  }
```
> Change `RELEASE_TAG` placeholder to the recommended version tag.


Then, You have to install the appropriate database library. By default, it's set to `mysql2` but you can change this as needed.

```shell
$ npm install mysql2 --save
```
or 
```shell
$ npm install pg --save
$ npm install pg-native --save
$ npm install sqlite3 --save
$ npm install better-sqlite3 --save
$ npm install mysql2 --save
$ npm install oracledb --save
$ npm install tedious --save
```

## How to use

Require the utility at the top of your file as follows:
```js
const QueryManager = require('ac-utils/src/query-manager');
```

### Query Manager instanciation
There are few ways you can instanciate a query manager using this utility. It all depends on the nature of your use-case.

#### Heavy and moderate use-case usage
Ideal for those lambda functions that are heavily and moderated used.

- In this setup, it will reuse the same query manager instance across the different lambda invocations therefor will reduce the initial DB connections to only one.
- You should NEVER destroy the connection manually.

```js
// Outside of the handler.
const queryManager = new QueryManager();

module.exports.handler = async (event, context) => {
  // Initiate only once per lambda instance and reused across lambda invocations.
  queryManager.initOnce({
    host: DB_HOST,
    user: DB_USER,
    password: DB_PASSWORD,
    database: DATABASE
  });

  // Use queryManager....
}
```

#### Low or sporadic use-case usage
Ideal for those lambda functions that are rarely or low used.

- In this setup, it will create a new connection on every invocation.
- You should Always destroy the connection manually.

```js
// Outside of the handler.
const queryManager = new QueryManager();

module.exports.handler = async (event, context) => {
  // Initiate once per lambda invocation.
  queryManager.init({
    host: DB_HOST,
    user: DB_USER,
    password: DB_PASSWORD,
    database: DATABASE
  });

  // Use queryManager....

  // Explicitly teardown the connection pool
  queryManager.destroy();
}
```

### Usage

#### Query builder
As this utility wraps the knex.js library, you can leverage its [Query Builder interface](https://knexjs.org/guide/query-builder.html)
```js
  // Get the builder
  const builder = queryManager.getBuilder();
  // builder usage - pure knex.js syntax
  const result = await builder.table('MY_TABLE')
    .select('*')
    .limit(2);

  console.log(result);
```

#### Read full example
```js
  const QueryManager = require('ac-utils/src/query-manager');

  const queryManager = new QueryManager();

  module.exports.handler = async (event, context) => {
    queryManager.initOnce({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DATABASE
    });

    const builder = queryManager.getBuilder();

    const result = await builder.table('MY_TABLE')
      .select('*')
      .limit(2);

    console.log(result);
  }
```

#### Write full example (With retries and transaction/rollback features)
```js
  const QueryManager = require('ac-utils/src/query-manager');

  const queryManager = new QueryManager();

  module.exports.handler = async (event, context) => {
    queryManager.initOnce({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DATABASE
    });

    const builder = queryManager.getBuilder();

    const statement = builder.table('MY_TABLE')
      .insert({
        first_name: 'name',
        last_name: 'last name',
        email: '<EMAIL>',
      });

    const statement2 = builder('MY_SECOND_TABLE')
      .where('id', 1)
      .update({
        title: 'Ut a autem vero.',
      });

    const statement3 = builder.table('MY_TABLE')
      .where('first_name', 'name')
      .del();

    queryManager.addStatements([statement, statement2, statement3]);

    await queryManager.runStatements();
  }
```

#### Write full example 2 (With transaction/rollback feature)
```js
  const QueryManager = require('ac-utils/src/query-manager');

  const queryManager = new QueryManager();

  module.exports.handler = async (event, context) => {
    
    queryManager.initOnce({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DATABASE
    });

    // Direct access to the transaction object
    const response = await queryManager.transaction(async (trx) => {
      
      const insertResponse = await trx.table('authors')
        .insert([{
          first_name: 'name1',
          last_name: 'last name 1',
          email: '<EMAIL>',
          birthdate: dformat,
        },
        {
          first_name: 'name1',
          last_name: 'last name 1',
          email: '<EMAIL>',
          birthdate: dformat,
        }]);

      const selectResponse = await trx.raw("select * from authors where email = '<EMAIL>' or email = '<EMAIL>'")
      const deleteResponse = await trx.raw("delete from `authors` where `id` > 100");

      return [insertResponse, selectResponse, deleteResponse]
    });

  console.log(response);
  }
```

#### Write full example 3
```js
  const QueryManager = require('ac-utils/src/query-manager');

  const queryManager = new QueryManager();

  module.exports.handler = async (event, context) => {
    
    queryManager.initOnce({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DATABASE
    });

    // Direct access to the transaction object
    const trx = await queryManager.transaction();

    const insertResponse = await trx.table('authors')
      .insert([{
        first_name: 'name1',
        last_name: 'last name 1',
        email: '<EMAIL>',
        birthdate: dformat,
      },
      {
        first_name: 'name1',
        last_name: 'last name 1',
        email: '<EMAIL>',
        birthdate: dformat,
      }]);

    const selectResponse = await trx.raw("select * from authors where email = '<EMAIL>' or email = '<EMAIL>'")
    const deleteResponse = await trx.raw("delete from `authors` where `id` > 100");

    trx.commit();
    // trx.rollback();

    console.log([insertResponse, selectResponse, deleteResponse]);
  }
```

> You can find more examples in the examples directory.

#### Query Manager Interface
Beside exposing the knex query builder, Query Manager comes with its own interface:

- `initOnce` - Iniciates the query manager only if it has not been iniciate previously.
- `init` - Iniciates the query manager.
- `isConnected` - Method that tells you if you have already established a connection to the database.
- `reConnect` - Useful if need to perform DB operations but the connection has been lost or destroyed.
- `destroy` - Explicitly teardown the connection pool.
- `addRawStatements` - Adds RAW SQL statements to be executed.
- `addStatements` - Adds SQL Knex-like statements to be executed.
- `clearStatements` - Remove all SQL statements you previosly added.
- `getStatements` - Returns all SQL statements you have in the manager.
- `getBuilder` | `getKnex` - Gets the manager knex instance.
- `getMigrate` - Shortcut to the knex class utilized by the knex migrations.
- `runStatements({ retryAttempts = 3, retriesDelay = retriesDelayFunction })` - Executes in order and within a transaction all the statements in the manager.
- `runStatementsInParallel({ retryAttempts = 3, batchSize = 1000, retriesDelay = retriesDelayFunction })` - Executes in parallel and within a transaction all the statements in the manager.
- `runStatementsWithoutTransaction` - Executes in order all the statements in the manager.
- `runStatementsWithoutTransactionInParallel` - Executes in parallel all the statements in the manager.
- `execute` - Receives a callback with the knex instance as parameter.
- `transaction` - Receives a callback with the transaction instance as parameter. If no callback is provided, the transaction instance is returned.
- `listPendingMigrations` - Returns the pending migrations.
- `runMigrations` - Executes the pending migrations.