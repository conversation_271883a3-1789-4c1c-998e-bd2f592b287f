const QueryManager = require('../index.js');
const { getFormatedDate } = require('./helper')

const queryManager = new QueryManager({
  host: '127.0.0.1',
  user: 'root',
  password: 'rootroot',
  database: 'test',
  // debug: true
});

const dformat = getFormatedDate();

module.exports.writesInCallback = async (event, context) => {

  queryManager.addStatements((builder) => {

    const statement1 = builder.table('authors')
      .insert({
        first_name: 'name1',
        last_name: 'last name 1',
        email: '<EMAIL>',
        birthdate: dformat,
      });

    const statement2 = builder('posts')
      .where('id', 1)
      .update({
        title: 'Ut a autem vero.',
      });

    const statement3 = builder('posts')
      .where('id', 2)
      .update({
        title: 'Aut dolorem facere sunt molestiae voluptatem quo.',
      });

    const statement4 = builder.table('authors')
      .where('first_name', 'name1')
      .del();

    return [statement1, statement2, statement3, statement4];
  })


  //console.log(queryManager.getStatements())

  await queryManager.runStatements();
  // await queryManager.runStatementsInParallel();
  // await queryManager.runStatementsWithoutTransaction();
  // await queryManager.runStatementsWithoutTransactionInParallel();
}

module.exports.writesWithBuilder = async (event, context) => {

  const builder = queryManager.getBuilder();

  const statement = builder.table('authors')
    .insert({
      first_name: 'name1',
      last_name: 'last name 1',
      email: '<EMAIL>',
      birthdate: dformat,
    });

  const statement2 = builder('posts')
    .where('id', 1)
    .update({
      title: 'Ut a autem vero.',
    });

  const statement3 = builder.table('authors')
    .where('first_name', 'name1')
    .del();

  queryManager.addStatements([statement, statement2, statement3]);

  // console.log(queryManager.getStatements())

  await queryManager.runStatements();
  //await queryManager.runStatementsInParallel();
  //await queryManager.runStatementsWithoutTransaction();
  //await queryManager.runStatementsInParallel();
}

module.exports.writesWithRawSQL = async (event, context) => {
  const builder = queryManager.getBuilder();

  const statement = builder.table('authors')
    .insert([{
      first_name: 'name1',
      last_name: 'last name 1',
      email: '<EMAIL>',
      birthdate: dformat,
    },
    {
      first_name: 'name1',
      last_name: 'last name 1',
      email: '<EMAIL>',
      birthdate: dformat,
    }]);
  const statements = [
    statement.toString(),
    "select id from authors where email = '<EMAIL>' or email = '<EMAIL>'",
    "delete from `authors` where `id` > 100"
  ]

  queryManager.addRawStatements(statements)

  // console.log(queryManager.getStatements())

  //const response = await queryManager.runStatements();
  //const response = await queryManager.runStatementsInParallel();
  //const response = await queryManager.runStatementsWithoutTransaction();
  const response = await queryManager.runStatementsInParallel();
  console.log(response);
  //console.log(JSON.stringify(response));
}

module.exports.writesWithTransactionHelper = async (event, context) => {
  const response = await queryManager.transaction(async (trx) => {
    const insertResponse = await trx.table('authors')
      .insert([{
        first_name: 'name1',
        last_name: 'last name 1',
        email: '<EMAIL>',
        birthdate: dformat,
      },
      {
        first_name: 'name1',
        last_name: 'last name 1',
        email: '<EMAIL>',
        birthdate: dformat,
      }]);

    const selectResponse = await trx.raw("select * from authors where email = '<EMAIL>' or email = '<EMAIL>'")
    const deleteResponse = await trx.raw("delete from `authors` where `id` > 100");

    return [insertResponse, selectResponse, deleteResponse]
  });

  console.log(response);

}

module.exports.writesWithTransactionObject = async (event, context) => {
  const trx = await queryManager.transaction();

  const insertResponse = await trx.table('authors')
    .insert([{
      first_name: 'name1',
      last_name: 'last name 1',
      email: '<EMAIL>',
      birthdate: dformat,
    },
    {
      first_name: 'name1',
      last_name: 'last name 1',
      email: '<EMAIL>',
      birthdate: dformat,
    }]);

  const selectResponse = await trx.raw("select * from authors where email = '<EMAIL>' or email = '<EMAIL>'")
  const deleteResponse = await trx.raw("delete from `authors` where `id` > 100");

  trx.commit();
  // trx.rollback();

  console.log([insertResponse, selectResponse, deleteResponse]);

}


// Local testing purposes
(async () => {
  // await this.writesInCallback('', '')
  // await this.writesWithBuilder('', '')
  // await this.writesWithRawSQL('', '')
  //await this.writesWithTransactionHelper('', '')
  await this.writesWithTransactionObject('', '')


  // console.log(await queryManager.isConnected());
  queryManager.getBuilder().destroy();
})()
