const QueryManager = require('../index.js');

const queryManager = new QueryManager({
  host: '127.0.0.1',
  user: 'root',
  password: 'rootroot',
  database: 'test',
  // debug: true,
});

module.exports.reader = async (event, context) => {
  const builder = queryManager.getBuilder();

  const result = await builder.table('authors')
    .select('*')
    .limit(1);

  console.log(result)
}


// Local testing purposes
(async () => {
  await this.reader('', '')

  // console.log(await queryManager.isConnected());
  queryManager.getBuilder().destroy();
})()
