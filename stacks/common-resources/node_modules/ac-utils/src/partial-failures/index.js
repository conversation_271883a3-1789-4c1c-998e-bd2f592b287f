'use strict'

const { asyncMapRunInParallelInChunks, asyncForEach } = require('../async');

const AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE = process.env.AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE;

const sleep = (n) => {
  return new Promise(function(resolve){
    setTimeout(resolve,n);
  });
}

const processInSequence = async ({ list, callback, retries = 3, retriesDelay = retriesDelayFunction }) => {

  const failedItems = [];
  const responses = [];

  await asyncForEach(list, async (item) => {
    responses.push(await internalFunction({ item, retries, retriesDelay, callback, failedItems }))
  });

  return {
    responses,
    failures: failedItems
  };
}

const processInBatches = async ({ list, batchSize = 10, callback, retries = 3, retriesDelay = retriesDelayFunction }) => {
  const failedItems = [];
  const responses = [];

  await asyncMapRunInParallelInChunks(list, batchSize, async (item) => {
    responses.push(await internalFunction({ item, retries, retriesDelay, callback, failedItems }))
  });

  return {
    responses,
    failures: failedItems
  };
}

const internalFunction = async ({ item, retries, retriesDelay, callback, failedItems }) => {
  let retry = 0;
  try {
    while (retries > retry) {
      try {
        const callbackResponse = await callback(item);

        return callbackResponse;
      } catch (err) {
        if (AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE)
          console.log(`Retrying => attempt: ${retry}, item: ${JSON.stringify(item)}`);

        await sleep(retriesDelay(retry));

        retry++;
        if (retries == retry) {
          throw err;
        }
      }
    }
  } catch (err) {
    failedItems.push({ item, error: err });

    if (AC_UTILS_PARTIAL_FAILURES_DEBUG_MODE)
      console.log(`Failed after ${retries} retries, item: ${JSON.stringify(item)}`);
  }
}

const retriesDelayFunction = (retry) => 1000 * (retry / 2);

module.exports = {
  processInSequence,
  processInBatches
}