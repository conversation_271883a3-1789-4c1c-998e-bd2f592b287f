'use strict'
const { processInSequence, processInBatches } = require('./index');


const list = [1, 2, 3, 4];

(async () => {
  const startDate = new Date();

  const response = await processInSequence({
    list,
    callback: (item) => {
      return myFunction(item)
    },
    retries: 3,
    retriesDelay: (retry) => 500
  })

  const endDate = new Date();
  const seconds = (endDate.getTime() - startDate.getTime()) / 1000;

  console.log(response);
  console.log(`execution time: ${seconds}`);

  const startDateBatch = new Date();

  // ***************************** //
  // process in batches
  const responseBatch = await processInBatches({
    list,
    callback: (item) => {
      return myFunction(item)
    },
    retries: 3,
    retriesDelay: (retry) => 500
  })

  const endDateBatch = new Date();
  const secondsBatch = (endDateBatch.getTime() - startDateBatch.getTime()) / 1000;

  console.log(responseBatch);
  console.log(`execution time: ${secondsBatch}`);
})()

const myFunction = async (item) => {
  const responsePromise = await new Promise(async (resolve, reject) => {
    // if (item === 3 || item === 1)
    // reject('test');

    setTimeout(() => resolve(`my item ${item}`), 1000)
  })

  if (item === 3 || item === 1)
    throw Error('test-2');

  return responsePromise;
}