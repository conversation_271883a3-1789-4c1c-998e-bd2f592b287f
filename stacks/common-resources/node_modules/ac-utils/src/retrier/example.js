'use strict'
const retrier = require('./index');


const myFunction = async (item) => {
  const responsePromise = await new Promise(async (resolve, reject) => {
    if (item === 3 || item === 1)
    reject('test');

    setTimeout(() => resolve(`my item ${item}`), 1000)
  })

  // if (item === 3 || item === 1)
  //   throw Error('test-2');

  return responsePromise;
}

(async () => {
  const response = await retrier({ callback: () => myFunction(1), debug: true });

  console.log(response);
})()