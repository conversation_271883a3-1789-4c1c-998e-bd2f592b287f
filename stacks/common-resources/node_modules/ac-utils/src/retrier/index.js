'use strict'

const retrier = async ({ callback, attempts = 3, exponentialBackoff = exponentialBackoffFunction, debug = false }) => {
  let retry = 0;

  try {
    while (attempts > retry) {
      try {
        const callbackResponse = await callback();

        return callbackResponse;
      } catch (err) {
        if (debug)
          logMessage({
            message: 'Retrier - Retrying',
            params: {
              currentAttempt: retry,
              totalAttempt: attempts,
            },
            debug
          })

        await sleep(exponentialBackoff(retry));

        retry++;
        if (attempts == retry) {
          throw err;
        }
      }
    }
  } catch (err) {
    logMessage({
      message: 'Retrier - Error after tries',
      params: {
        currentAttempt: retry,
        totalAttempt: attempts,
      },
      debug
    })

    throw err;
  }
}

const logMessage = ({ message, params = {}, debug = false }) => {
  if (!debug) {
    return
  }

  const logMsg = Object.assign({}, params);
  logMsg.message = message;

  console.log(JSON.stringify(logMsg));
}

const exponentialBackoffFunction = (retry) => 1000 * (retry / 2);

const sleep = (n) => {
  return new Promise(function (resolve) {
    setTimeout(resolve, n);
  });
}

module.exports = retrier