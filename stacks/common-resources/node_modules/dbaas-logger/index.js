/*
    Author      : <PERSON><PERSON>
    CreatedDate : Sept 29 2019
    Description :  Used for ELK- stream logs to Kibana
    Modified by : <PERSON><PERSON><PERSON><PERSON>
    Description : 
        - Refactoring. 
        - Removal of external service functions. 
        - Addition of explicit parameters to define service type. 
        - Error handling. 
    Modified by : Meenal Gangrade
    Description : 
        - removed code smells
    Modified by : <PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON>
    ModifiedDate: Dec 23 2021
    Description :
        - Addition of stringify logic to handle circular JSON objects
        - Error handling and logging
        - Parameter validation
        - Controlled logging
        - Replaced uuidv4 with uuid npm package
    Modified by : <PERSON><PERSON><PERSON><PERSON>
    ModifiedDate: Mar 07 2022
    Description :
        - Addition of uploadObjectToS3 method to upload large data objects to S3
        - Replaced INFO with DEBUG when debug mode is enabled
    Modified by: <PERSON><PERSON><PERSON><PERSON>
    Modification Date: Mar 14 2022
    Description : 
        - Issue: Headers coming from AWS services contain security token information sent via client( mobile/web ). Need to make sure it is not printed.
        - Removing logging of headers object as part of common event information. 
        - Note: This is a quick fix. For a permanent fix, we will have to write a method to cleanup headers which will retain only allowed headers and will remove everything else. 
            - We will take another story( with defined requirements ) to finalize on list of allowed headers.
    Modified by : <PERSON><PERSON><PERSON><PERSON>
    ModifiedDate: Aug 04 2022
    Description :
        - Automatic Log Compression
    Modified by : Karan Prajapat / Saurabh Vinchurkar
    ModifiedDate: Sep 20 2022
    Description:
        - Addition of addFunctionalKeys method to provide key attribute relevant for searching logs for service team (like PNR, Flight No, FFP).
    Modified by : Vignesh R / Saurabh Vinchurkar
    ModifiedDate: Oct 17 2022
    Description:
        - Added the encryptData and decryptData function for encrypting and decrypting the JSON, XML and string data.
    Modified by : Naman Shrivastava
    Modifcation date: July 03 2023
    Description:
        - Created a new version to support aws-sdk v3 using modular packages.
        - Removed aws-sdk and only using client packages that are needed (@aws-sdk/client-kinesis, @aws-sdk/client-kms, @aws-sdk/client-s3).
        - Modified methods of these client packages to use promises, async await instead of callbacks which were used in aws-sdk v2.
    Modified by : Naman Shrivastava
    Modifcation date: September 21 2023
    Description:
        - Removed zipped and split methods from exports
    Modified by : Divyansh Dharmadhikari
    Modifcation date: May 9 2024
    Description:
        - Added 'maskData' method for masking of PII data.
        - This method will take a input in either JSON/XML/String format. It will create a deep clone of that object and the mask the given fields.

    Modified by : Bikash Sarkar/ Divyansh Dharmadhikari
    Modifcation date: September 4 2024
    Description:
        - Added 'logMessageToSqs method added
        - This method send the dataobject to the SQS given in the environmnet variable
    Modified by : Bikash Sarkar/ Divyansh Dharmadhikari
    Modifcation date: September 20 2024
    Description:
        - Added 'sendObjectToSentinelS3 method added
        - This method send the dataobject to the sentinel S3 given in the environmnet variable
    Modified by : Ankush Choudhary
    Modifcation date: December 20 2024
    Description:
        - Updated import statement to use node:zlib
        - To resolve zlib conflicts and use Node.js built-in module.
*---------------------------------------------------------------------------------------
*/

'use strict';

//modules
const crypto = require('crypto');
const xml2js = require('xml2js');
const util = require('util');
const md5 = require("md5");
const { v4 } = require("uuid");
const THRESHOLD_SIZE = 1000000; //1 MB
const streamRegion = process.env.STREAM_REGION || 'us-east-2';
const {KinesisClient, PutRecordCommand} = require("@aws-sdk/client-kinesis");
const kinesisClient = new KinesisClient({region: streamRegion});                                             // const kinesis = new AWS.Kinesis(streamRegion);
const {S3Client, PutObjectCommand} = require("@aws-sdk/client-s3");
const s3Client = new S3Client();                                                                            // const s3 = new AWS.S3();
const zlib = require('node:zlib');
const loki = require("lokijs");
const lokiDBJson = new loki('loki.json');
const lokiDB = lokiDBJson.addCollection('logs');
const map = new Map();
const mask = require('maskdata');
const randomize = require('randomatic');
const dbaasLoggerConfig = { streamName: "not set", debugMode: false};
const {KMSClient, EncryptCommand, DecryptCommand, GenerateDataKeyCommand} = require("@aws-sdk/client-kms");
const kmsClient = new KMSClient();                                                                          // const kms = new AWS.KMS();
const kmsGlobalData = {
    encryption: {
        encryptedDataKey: null,
        unencryptedDataKey: null
    },
    decryption: {
        encryptedDataKey: null,
        unencryptedDataKey: null
    }
};
const builder = new xml2js.Builder();
xml2js.parseStringPromise = util.promisify(xml2js.parseString);
const {SQSClient, SendMessageCommand} = require("@aws-sdk/client-sqs");
const { resolve } = require('path');
const { rejects } = require('assert');
const sqsClient = new SQSClient();

// NOLOGS, INFO, DEBUG, WARN, ERROR, CRITICAL
const lambdaLogMode = (process.env.LAMBDA_LOG_MODE || "NOLOGS").toLowerCase();
const dbaasLoggerVersion = "vstable_4.0.7";

const dbaasLogLevels = {
    debug: "debug",
    info: "info",
    warn: "warn",
    critical: "critical",
    error: "error"
};

function shouldPrintLogs(allowedLogggingLevelsForMethod){
    try { 
        return allowedLogggingLevelsForMethod.includes(lambdaLogMode);
    } catch(error) {
        consoleLoggerError("DBAAS LOGGING - Error while processing log level ", error);
        return true; //LOG EVERYTHING if this causes exception to be able to debug issue in DBAAS Logger
    }
}

/**
    Prints critical logs to console
    Gets printed in all levels of logs except NO-LOGS. 
    Critical logs have same level as error, but since they are not errors, they are separated.
    Examples are:
        - Request / Response to external data provider
*/
function consoleCritical() {
    try {
        //This states when the critical logs will be printed. 
        const positiveScenarios = ["critical", "warn", "debug", "info", "loggererror"]; 
        if (shouldPrintLogs(positiveScenarios)) {
            if (arguments.length === 0) {
                console.log();
            } else if (arguments.length === 1) {
                console.log(arguments[0]);
            } else if (arguments.length === 2) {
                console.log(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.log(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.log(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.log([...arguments].join(" "));
            }
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in consoleCritical function: ", error);
    }
}

/**
    Prints critical logs to console
    Gets printed in all levels of logs except NO-LOGS. 
    Critical logs have same level as error, but since they are not errors, they are separated.
    Examples are:
        - Request / Response to external data provider
*/
function printZippedDataCritical(logEvent, objectName, logType, uniqueRequestId) {
    try {
        //This states when the critical logs will be printed. 
        const positiveScenarios = ["critical", "warn", "debug", "info", "loggererror"]; 
        if (positiveScenarios.includes(lambdaLogMode)) {
            printZippedData(logEvent, objectName, logType, uniqueRequestId, console.log);
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in printZippedDataCritical function: ", error);
    }
}

/**
    Prints ERROR logs to console
    Gets printed in all levels of logs except NO-LOGS. 
    It is  intended to print only errors. It will be called from catch blocks or from logServiceError* methods of dbaas-logger
    Examples are:
        - Error occurrence while fetching Response from external data provider
        - Any generic Exception
*/
function consoleError() {
    try {
        const positiveScenarios = ["critical", "error", "warn", "debug", "info", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            if (arguments.length === 0) {
                console.error();
            } else if (arguments.length === 1) {
                console.error(arguments[0]);
            } else if (arguments.length === 2) {
                console.error(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.error(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.error(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.error([...arguments].join(" "));
            }
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in consoleError function: ", error);
    }
}

/**
    Prints ERROR logs to console
    Gets printed in all levels of logs except NO-LOGS. 
    It is  intended to print only errors. It will be called from catch blocks or from logServiceError* methods of dbaas-logger
    Examples are:
        - Error occurrence while fetching Response from external data provider
        - Any generic Exception
*/
function printZippedDatError(logEvent, objectName, logType, uniqueRequestId) {
    try {
        const positiveScenarios = ["critical", "error", "warn", "debug", "info", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            printZippedData(logEvent, objectName, logType, uniqueRequestId, console.error);
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in printZippedDatError function: ", error);
    }
}

/**
    Prints ERROR logs to console
    Gets printed in all levels of logs except NO-LOGS. 
    It is  intended to print only errors. It will be called from catch blocks or from logServiceError* methods of dbaas-logger
    Examples are:
        - Error occurrence while fetching Response from external data provider
        - Any generic Exception
*/
function consoleLoggerError() {
    try {
        const positiveScenarios = ["loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            if (arguments.length === 0) {
                console.error();
            } else if (arguments.length === 1) {
                console.error(arguments[0]);
            } else if (arguments.length === 2) {
                console.error(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.error(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.error(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.error([...arguments].join(" "));
            }
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in consoleLoggerError function: ", error);
    }
}

/**
    Prints critical, error and warning logs to console
*/
function consoleWarn() {
    try {
        const positiveScenarios = ["warn", "debug", "info", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            if (arguments.length === 0) {
                console.warn();
            } else if (arguments.length === 1) {
                console.warn(arguments[0]);
            } else if (arguments.length === 2) {
                console.warn(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.warn(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.warn(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.warn([...arguments].join(" "));
            }
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in consoleWarn function: ", error);
    }
}

/**
    Prints critical, error and warning logs to console
*/
function printZippedDataWarn(logEvent, objectName, logType, uniqueRequestId) {
    try {
        const positiveScenarios = ["warn", "debug", "info", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            printZippedData(logEvent, objectName, logType, uniqueRequestId, console.warn);
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in printZippedDataWarn function: ", error);
    }
}

/**
    Prints critical, error, warning and debug logs to console
*/
function consoleDebug() {
    try {
        const positiveScenarios = ["debug", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            if (arguments.length === 0) {
                console.debug();
            } else if (arguments.length === 1) {
                console.debug(arguments[0]);
            } else if (arguments.length === 2) {
                console.debug(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.debug(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.debug(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.debug([...arguments].join(" "));
            }
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in consoleDebug function: ", error);
    }
}

/**
    Prints critical, error, warning and debug logs to console
*/
function printZippedDataDebug(logEvent, objectName, logType, uniqueRequestId) {
    try {
        const positiveScenarios = ["debug", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            printZippedData(logEvent, objectName, logType, uniqueRequestId, console.debug);
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in printZippedDataDebug function: ", error);
    }
}

/**
    Prints critical, error, warning, debug and info logs to console
*/
function consoleInfo() {
    try {
        const positiveScenarios = ["info", "debug", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode) ) {
            if (arguments.length === 0) {
                console.log();
            } else if (arguments.length === 1) {
                console.log(arguments[0]);
            } else if (arguments.length === 2) {
                console.log(arguments[0], arguments[1]);
            } else if (arguments.length === 3) {
                console.log(arguments[0], arguments[1], arguments[2]);
            } else if (arguments.length === 4) {
                console.log(arguments[0], arguments[1], arguments[2], arguments[3]);
            } else {
                console.log([...arguments].join(" "));
            }
        }
    } catch(error) {
        console.error("DBAAS LOGGING - Error in consoleInfo function: ", error);
    }
}

/**
    Prints critical, error, warning, debug and info logs to console
*/
function printZippedDataInfo(logEvent, objectName, logType, uniqueRequestId) {
    try {
        const positiveScenarios = ["info", "debug", "loggererror"];
        if (positiveScenarios.includes(lambdaLogMode)) {
            printZippedData(logEvent, objectName, logType, uniqueRequestId, console.info);
        }
    } catch(error) {
        console.error("DBAAS LOGGING - Error in printZippedDataInfo function: ", error);
    }
}

/**
    Validates if the data is of string type or not
        @param data     (any type)          : data that we want to validate
        @param dataKey  (string / number)   : keyname of the data
        @param logType  (string)            : logtype name eg start, logMessage, logServiceRequest etc
*/
const isString = (data, dataKey, logType) => {
    if (data && typeof data === "string") {
        return true;
    } else {
        consoleLoggerError("DBAAS LOGGING - Error in '" + logType + "' log: " + dataKey + " value should be string");
    }
};

/**
    Validates if the data is of object type but not array
        @param data     (any type)          : data that we want to validate
        @param dataKey  (string / number)   : keyname of the data
*/
const isObjectNotArray = (data, dataKey) => {
    if (data && typeof data === "object" && (!Array.isArray(data))) {
        return true;
    } else {
        throw new Error(dataKey + " value should be JSON / XML object");
    }
};

/**
    Validates if the globalObject has ctx.awsRequestId key or not
        @param global   (JSON object)      : global object
*/
const validateGlobalObject = (global) => {
    if (global && global.ctx && global.ctx.awsRequestId) {
        return true;
    } else {
        throw new Error("ctx.awsRequestId not present in globalObject");
    }
};

/**
    Replacer function for JSON.stringify function
*/
function serializer(replacer, cycleReplacer) {
    try {
        const stack = [],
            keys = [];
    
        if (cycleReplacer == null) cycleReplacer = function(key, value) {
            if (stack[0] === value) return "[Circular ~]";
            return "[Circular ~." + keys.slice(0, stack.indexOf(value)).join(".") + "]";
        };
    
        return function(key, value) {
            if (stack.length > 0) {
                const thisPos = stack.indexOf(this);
                ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);
                ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);
                if (~stack.indexOf(value)) value = cycleReplacer.call(this, key, value);
            }
            else stack.push(value);
    
            return replacer == null ? value : replacer.call(this, key, value);
        };
    } catch (error) {
        throw new Error("Error in serializer function");
    }
}

/**
    Stringifies data and circual JSON objects
        @param dataObject       (any type)      : data that we want to stringify
        @param logType          (string)        : function name from where it is getting called
        @param uniqueRequestId  (string)        : unique requestid for particual log (request / response / error)
*/
const strigifyObject = (dataObject, logType, uniqueRequestId) => {
    let strigifiedObject;
    try {
        try {
            strigifiedObject = JSON.stringify(dataObject);
            return strigifiedObject;
        } catch (err) {
            if (uniqueRequestId) {
                consoleWarn("DBAAS LOGGING - Circular dependency found in " + logType + ", uniqueRequestId: " + uniqueRequestId);
            } else {
                consoleWarn("DBAAS LOGGING - Circular dependency found in " + logType);
            }
            strigifiedObject = JSON.stringify(dataObject, serializer());
            return strigifiedObject;
        }
    } catch (error) {
        if (uniqueRequestId) {
            consoleLoggerError("DBAAS LOGGING - Unable to stringify circular object, logType " + logType + ", uniqueRequestId: " + uniqueRequestId);
        } else {
            consoleLoggerError("DBAAS LOGGING - Unable to stringify circular object, logType " + logType);
        }
        if (logType === "sendLogsToKinesis") {
            throw error;
        }
        return "Circular dependency found";
    }
};

// Module for Logging logs into ELK stack.

const getMinimalContext = (context) => {
    try {
        let minimalContext = {};
        minimalContext.functionName = context.functionName;
        minimalContext.awsRequestId = context.awsRequestId;
        return minimalContext;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error while extracting AWS Requst Id and Lambda name from context : ", error);
        throw error;
    }
};

/**
    Creates logEvent object with common key-value assignments
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
        @param logType          (string)        : function name from where it is getting called
*/
const getEventWithBasicInformation = (global, serviceType, serviceName, message, logType) => {
    try {
        isObjectNotArray(global, "globalObject");
        validateGlobalObject(global);
        try {
            isString(serviceType, "serviceType", logType);
            isString(serviceName, "serviceName", logType);
            isString(message, "message", logType);    
        } catch (err) {
            consoleLoggerError("DBAAS LOGGING - Error while validating serviceType, serviceName, message parameters ", err);
        }
        const logEvent = new Object();
        logEvent.logSource = 'sre'; 
        logEvent.arguments = global.arguments;
        logEvent.context = getMinimalContext(global.ctx);
        logEvent.timestamp = new Date().toISOString();
        logEvent.serviceType = serviceType;
        logEvent.serviceName = serviceName;
        logEvent.message = message;
        logEvent.type = logType;
        logEvent.functionalKeys = global.functionalKeys;
        return logEvent;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error while initializing the dbaasLogger logEvent object: ", error);
        throw error;
    }
};

/**
    Creates logEvent object with common key-value assignments and add only context object
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
        @param logType          (string)        : function name from where it is getting called
*/
const getEventWithBasicInformationContextOnly = (global, serviceType, serviceName, message, logType) => {
    try {
        isObjectNotArray(global, "globalObject");
        validateGlobalObject(global);
        try {
            isString(serviceType, "serviceType", logType);
            isString(serviceName, "serviceName", logType);
            isString(message, "message", logType);    
        } catch (err) {
            consoleLoggerError("DBAAS LOGGING - Error while validating serviceType, serviceName, message parameters ", err);
        }
        const logEvent = new Object();
        logEvent.logSource = 'sre';
        logEvent.context = getMinimalContext(global.ctx);
        logEvent.timestamp = new Date().toISOString();
        logEvent.serviceType = serviceType;
        logEvent.serviceName = serviceName;
        logEvent.message = message;
        logEvent.type = logType;
        logEvent.functionalKeys = global.functionalKeys;
        return logEvent;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error while initializing the dbaasLogger logEvent contextOnly object: ", error);
        throw error;
    }
};


const isV3Enabled = () => {
    return process.env.LAYER_ENABLED == "true" || process.env.LAYER_ENABLED == true;
};

const isV2Enabled = () => {
    return !isV3Enabled();
};

const shouldPrintConsole = () => {
    //If V3 logging is enabled, console must be printed. 
    if(isV3Enabled()){
        return true;
    }
    //If V2 logging is enabled, console should be printed only if debug mode is enabled.   
    if(isV2Enabled() && dbaasLoggerConfig.debugMode == 'true' ){
        return true;
    }
};

const printLogsWithSpecificLogLevel = (finalLogLevel, callback) => {
    switch (finalLogLevel) {
        case dbaasLogLevels.debug:
            consoleDebug(callback);
            break;
        case dbaasLogLevels.info:
            consoleInfo(callback);
            break;
        case dbaasLogLevels.critical:
            consoleCritical(callback);
            break;
        case dbaasLogLevels.warn:
            consoleWarn(callback);
            break;
        case dbaasLogLevels.error:
            consoleError(callback);
            break;
        default:
            consoleInfo(callback);
    }
};

const printZippedLogsWithSpecificLogLevel = (finalLogLevel, logEvent, objectName, logType, uniqueRequestId) => {
    switch (finalLogLevel) {
        case dbaasLogLevels.debug:
            printZippedDataDebug(logEvent, objectName, logType, uniqueRequestId, console.debug);
            break;
        case dbaasLogLevels.info:
            printZippedDataInfo(logEvent, objectName, logType, uniqueRequestId, console.info);
            break;
        case dbaasLogLevels.critical:
            printZippedDataCritical(logEvent, objectName, logType, uniqueRequestId, console.info);
            break;
        case dbaasLogLevels.warn:
            printZippedDataWarn(logEvent, objectName, logType, uniqueRequestId, console.warn);
            break;
        case dbaasLogLevels.error:
            printZippedDatError(logEvent, objectName, logType, uniqueRequestId, console.error);
            break;
        default:
            printZippedDataInfo(logEvent, objectName, logType, uniqueRequestId, console.info);
    }
};

const isCompressionLoggingRequired = (strigifiedObject) => {
    try {
        const messageSizeLimit = process.env.MESSAGE_SIZE_LIMIT || 50000;
        if (strigifiedObject.length > messageSizeLimit) {
            return true;
        }
        return false;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'isCompressionLoggingRequired' log: ", error);
        return false;
    }
};

/**
    Logs the start message, to be called at the beginning of the lambda
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
        @param inputArgument    ( JSON / XML / String) : Input arguments to lambda can be logged here
*/
const start = (global, serviceType, serviceName, message, inputArgument) => {
    try {
        const logEvent = getEventWithBasicInformation(global, serviceType, serviceName, message, "start");
        logEvent.context.dbaasLoggerVersion = dbaasLoggerVersion;
        logEvent.context.dbaasLoggingStreamName = dbaasLoggerConfig.streamName;
        logEvent.context.layerEnabled = process.env.LAYER_ENABLED;
        logEvent.inputArgument = inputArgument;
        if (inputArgument) {
            logEvent.arguments = null;
        }
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "start");
            if (isCompressionLoggingRequired(stringifiedObject)) {
                startZippedAndSplit(global, serviceType, serviceName, message, inputArgument);
            } else {
                consoleCritical(stringifiedObject);
            }
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'start' log: ", error);
    }
};

/**
    Logs the start message, to be called at the beginning of the lambda
    Compress the global.arguments object
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
        @param inputArgument    ( JSON / XML / String) : Input arguments to lambda can be logged here
*/
const startZippedAndSplit = (global, serviceType, serviceName, message, inputArgument) => {
    try {
        const logEvent = getEventWithBasicInformation(global, serviceType, serviceName, message, "startZippedAndSplit");
        logEvent.context.dbaasLoggerVersion = dbaasLoggerVersion;
        logEvent.context.dbaasLoggingStreamName = dbaasLoggerConfig.streamName;
        logEvent.context.layerEnabled = process.env.LAYER_ENABLED;
        logEvent.inputArgument = inputArgument;
        if (inputArgument) {
            logEvent.arguments = null;
        }
        if (shouldPrintConsole()) {
            if (inputArgument) {
                printZippedLogsWithSpecificLogLevel(dbaasLogLevels.critical, logEvent, "inputArgument", "startZippedAndSplit");
            } else {
                printZippedLogsWithSpecificLogLevel(dbaasLogLevels.critical, logEvent, "arguments", "startZippedAndSplit");
            }
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        } 
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'startZippedAndSplit' log: ", error);
    }
};

/**
    Logs the external API call request object, to be used while calling any entity outside the lambda and 
    returns a unique request-id
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param requestObject    (string / JSON / XML)   : request object that sent to external entity
        @param dataType         (string)                : data-type of requestObject
*/
const logServiceRequest = (global, serviceType, serviceName, message, requestObject, dataType, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "Request");
        logEvent.requestuniqueid = v4();
        logEvent.request = requestObject;
        if (dataType && typeof dataType === "string") {
            logEvent.dataType = dataType;    
        } else if (dataType) {
            logEvent.dataType = strigifyObject(dataType, "logServiceRequest", logEvent.requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequest' log, dataType is not of string type");
        } else {
            logEvent.dataType = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequest' log, dataType is undefined");
        }
        map.set((logEvent.requestuniqueid + "").toLowerCase().trim(), logEvent.timestamp);
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logServiceRequest", logEvent.requestuniqueid);
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logServiceRequestZippedAndSplit(global, serviceType, serviceName, message, requestObject, dataType, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
        return logEvent.requestuniqueid;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequest' log: ", error);
    }
};

/**
    Logs the external API call request object, to be used while calling any entity outside the lambda and returns a unique request-id
    Compress the requestObject object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param requestObject    (string / JSON / XML)   : request object that sent to external entity
        @param dataType         (string)                : data-type of requestObject
*/
const logServiceRequestZippedAndSplit = (global, serviceType, serviceName, message, requestObject, dataType, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "RequestZippedAndSplit");
        logEvent.requestuniqueid = v4();
        logEvent.request = requestObject;
        if (dataType && typeof dataType === "string") {
            logEvent.dataType = dataType;    
        } else if (dataType) {
            logEvent.dataType = strigifyObject(dataType, "logServiceRequestZippedAndSplit", logEvent.requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequestZippedAndSplit' log, dataType is not of string type");
        } else {
            logEvent.dataType = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequestZippedAndSplit' log, dataType is undefined");
        }
        map.set((logEvent.requestuniqueid + "").toLowerCase().trim(), logEvent.timestamp);
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
            printZippedLogsWithSpecificLogLevel(finalLogLevel, logEvent, "request", "logServiceRequestZippedAndSplit", logEvent.requestuniqueid);
        }
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
        return logEvent.requestuniqueid;
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceRequestZippedAndSplit' log: ", error);
    }
};

/**
    Logs the external API call response object, to be used when external API call succeeds
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param responseObject   (string / JSON / XML)   : response object received from external entity
        @param dataType         (string)                : data-type of requestObject
        @param requestuniqueid  (string)                : the unique-request-id returned by logServiceRequest log
*/
const logServiceResponse = (global, serviceType, serviceName, message, responseObject, dataType, requestuniqueid, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "Response");
        logEvent.response = responseObject;
        if (dataType && typeof dataType === "string") {
            logEvent.dataType = dataType;    
        } else if (dataType) {
            logEvent.dataType = strigifyObject(dataType, "logServiceResponse", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponse' log, dataType is not of string type");
        } else {
            logEvent.dataType = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponse' log, dataType is undefined");
        }
        if (requestuniqueid && typeof requestuniqueid === "string") {
            logEvent.requestuniqueid = requestuniqueid;    
        } else if (requestuniqueid) {
            logEvent.requestuniqueid = strigifyObject(requestuniqueid, "logServiceResponse", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponse' log, requestuniqueid is not of string type");
        } else {
            logEvent.requestuniqueid = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponse' log, requestuniqueid is undefined");
        }
        if (map.has((logEvent.requestuniqueid + "").toLowerCase().trim())) {
            const diff = getDifference(logEvent.timestamp, map.get((logEvent.requestuniqueid + "").toLowerCase().trim()));
            logEvent["ResponseTime"] = diff + "";
        }
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logServiceResponse", logEvent.requestuniqueid);
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logServiceResponseZippedAndSplit(global, serviceType, serviceName, message, responseObject, dataType, requestuniqueid, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponse' log: ", error);
    }
};

/**
    Logs the external API call response object, to be used when external API call succeeds
    Compress the responseObject object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param responseObject   (string / JSON / XML)   : response object received from external entity
        @param dataType         (string)                : data-type of requestObject
        @param requestuniqueid  (string)                : the unique-request-id returned by logServiceRequest log
*/
const logServiceResponseZippedAndSplit = (global, serviceType, serviceName, message, responseObject, dataType, requestuniqueid, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "ResponseZippedAndSplit");
        logEvent.response = responseObject;
        if (dataType && typeof dataType === "string") {
            logEvent.dataType = dataType;    
        } else if (dataType) {
            logEvent.dataType = strigifyObject(dataType, "logServiceResponseZippedAndSplit", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponseZippedAndSplit' log, dataType is not of string type");
        } else {
            logEvent.dataType = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponseZippedAndSplit' log, dataType is undefined");
        }
        if (requestuniqueid && typeof requestuniqueid === "string") {
            logEvent.requestuniqueid = requestuniqueid;    
        } else if (requestuniqueid) {
            logEvent.requestuniqueid = strigifyObject(requestuniqueid, "logServiceResponseZippedAndSplit", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponseZippedAndSplit' log, requestuniqueid is not of string type");
        } else {
            logEvent.requestuniqueid = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponseZippedAndSplit' log, requestuniqueid is undefined");
        }
        if (map.has((logEvent.requestuniqueid + "").toLowerCase().trim())) {
            const diff = getDifference(logEvent.timestamp, map.get((logEvent.requestuniqueid + "").toLowerCase().trim()));
            logEvent["ResponseTime"] = diff + "";
        }
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
            printZippedLogsWithSpecificLogLevel(finalLogLevel, logEvent, "response", "logServiceResponseZippedAndSplit", logEvent.requestuniqueid);
        } 
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceResponseZippedAndSplit' log: ", error);
    }
};

/**
    Logs the message and data-object to indicate and debug the flow
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param dataObject       (string / JSON / XML)   : (optional) additional object that developer want to log
*/
const logMessage = (global, serviceType, serviceName, message, dataObject, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "Message");
        logEvent.data = dataObject;
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logMessage");
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logMessageZippedAndSplit(global, serviceType, serviceName, message, dataObject, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logMessage' log: ", error);
    }
};

/**
    Logs the message and data-object and send the data-object to SQS
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param dataObject       (string / JSON / XML)   : (optional) additional object that developer want to log
*/
const logMessageToSqs = async (global, serviceType, serviceName, message, dataObject, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "MessageToSqs");
        logEvent.data = dataObject;
        const queueUrl = process.env.SENTINEL_SQS_URL;
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logMessageToSqs");
            if (isCompressionLoggingRequired(stringifiedObject)) {
                await sendToSqs(queueUrl, dataObject);
                logMessageZippedAndSplit(global, serviceType, serviceName, message, dataObject, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logMessageToSqs' log: ", error);
    }
};

/**
    Logs the message and data-object and send the data-object to SQS
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param dataObject       (string / JSON / XML)   : (optional) additional object that developer want to log
*/
const sendObjectToSentinelS3 = async (global, serviceType, serviceName, message, dataObject, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "ObjectToSentinelS3");
        logEvent.data = dataObject;
        const sentinelBucketName = process.env.SENTINEL_BUCKET_NAME;
        if(sentinelBucketName){
            await sendToSentinelS3(global,sentinelBucketName, dataObject);
        }
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "sendObjectToSentinelS3");
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logMessageZippedAndSplit(global, serviceType, serviceName, message, dataObject, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'sendObjectToSentinelS3' log: ", error);
    }
}


/**
-    Additional field to provide key attribute relevant for logs like PNR/FlightNo/FFP
-        @param global           (JSON Object)           : global object
-        @param serviceType      (string)                : service type of lambda
-        @param serviceName      (string)                : service name of lambda
-        @param message          (string)                : message to log
-        @param functionalKeys   (JSON Object)           : additional custom field object to provide key attributes that developer want to log
-*/
const addFunctionalKeys = (global, serviceType, serviceName, message, functionalKeys, statementLogLevel) => {
    try {
        isObjectNotArray(functionalKeys, "functionalKeys");
        global.functionalKeys = functionalKeys;
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "FunctionalKeys");
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
            printLogsWithSpecificLogLevel(finalLogLevel, strigifyObject(logEvent, "functionalKeys"));
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'functionalKeys' log: ", error);
    }
};

/**
    Logs the message and data-object to indicate and debug the flow
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param secretInformation (string)                : message to log
        @param dataObject       (string / JSON / XML)   : (optional) additional object that developer want to log
*/
const logSecretInformation = (global, serviceType, serviceName, message, secretInformation, statementLogLevel="critical") => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "SecretInformation");
        logEvent.data = secretInformation;
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
            printLogsWithSpecificLogLevel(finalLogLevel, strigifyObject(logEvent, "logSecretInformation"));
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logSecretInformation' log: ", error);
    }
};

/**
    Logs the message and data-object to indicate and debug the flow
    Compress the dataObject object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param dataObject       (string / JSON / XML)   : (optional) additional object that developer want to log
*/
const logMessageZippedAndSplit = (global, serviceType, serviceName, message, dataObject, statementLogLevel) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "MessageZippedAndSplit");
        logEvent.data = dataObject;
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.debug;
            printZippedLogsWithSpecificLogLevel(finalLogLevel, logEvent, "data", "logMessageZippedAndSplit");
        } 
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logMessageZippedAndSplit' log: ", error);
    }
};

/**
    Logs the error response with error-code and error object, to be used when external API call fails
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param errorCode        (string / number)       : error code of the error
        @param stackTrace       (string)                : error object
        @param requestuniqueid  (string)                : the unique-request-id returned by logServiceRequest log
*/
const logServiceError = (global, serviceType, serviceName, message, errorCode, stackTrace, requestuniqueid, statementLogLevel) => {
    try {
        //this.isCyclic(stackTrace);
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "Error");
        if (requestuniqueid && typeof requestuniqueid === "string") {
            logEvent.requestuniqueid = requestuniqueid;    
        } else if (requestuniqueid) {
            logEvent.requestuniqueid = strigifyObject(requestuniqueid, "logServiceError", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceError' log, requestuniqueid is not of string type");
        } else {
            logEvent.requestuniqueid = "undefined";
            consoleLoggerError("DBAAS LOGGING - Warn in 'logServiceError' log, requestuniqueid is undefined");
        }
        logEvent.dbaasErrorCode = errorCode;
        if (stackTrace && stackTrace.stack) //stackTrace;
            logEvent.dbaasErrorMessage = stackTrace.stack;
        else {
            logEvent.dbaasErrorMessage = stackTrace;
        }
        if (map.has((logEvent.requestuniqueid + "").toLowerCase().trim())) {
            const diff = getDifference(logEvent.timestamp, map.get((logEvent.requestuniqueid + "").toLowerCase().trim()));
            logEvent["ResponseTime"] = diff + "";
        }
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logServiceError", logEvent.requestuniqueid);
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logServiceErrorZippedAndSplit(global, serviceType, serviceName, message, errorCode, stackTrace, requestuniqueid, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.error;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceError' log: ", error);
    }
};

/**
    Logs the error response with error-code and error object, to be used when external API call fails
    Compress the stackTrace object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param errorCode        (string / number)       : error code of the error
        @param stackTrace       (string)                : error object
        @param requestuniqueid  (string)                : the unique-request-id returned by logServiceRequest log
*/
const logServiceErrorZippedAndSplit = (global, serviceType, serviceName, message, errorCode, stackTrace, requestuniqueid, statementLogLevel) => {
    try {
        //this.isCyclic(stackTrace);
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "ErrorZippedAndSplit");
        if (requestuniqueid && typeof requestuniqueid === "string") {
            logEvent.requestuniqueid = requestuniqueid;    
        } else if (requestuniqueid) {
            logEvent.requestuniqueid = strigifyObject(requestuniqueid, "logServiceErrorZippedAndSplit", requestuniqueid);
            consoleLoggerError("DBAAS LOGGING - Error in 'logServiceErrorZippedAndSplit' log, requestuniqueid is not of string type");
        } else {
            logEvent.requestuniqueid = "undefined";
            consoleLoggerError("DBAAS LOGGING - Warn in 'logServiceErrorZippedAndSplit' log, requestuniqueid is undefined");
        }
        logEvent.dbaasErrorCode = errorCode;
        if (stackTrace && stackTrace.stack) //stackTrace;
            logEvent.dbaasErrorMessage = stackTrace.stack;
        else {
            logEvent.dbaasErrorMessage = stackTrace;
        }
        if (map.has((logEvent.requestuniqueid + "").toLowerCase().trim())) {
            const diff = getDifference(logEvent.timestamp, map.get((logEvent.requestuniqueid + "").toLowerCase().trim()));
            logEvent["ResponseTime"] = diff + "";
        }
        //console.log(errorCode, message, stackTrace);
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.error;
            printZippedLogsWithSpecificLogLevel(finalLogLevel, logEvent, "dbaasErrorMessage", "logServiceErrorZippedAndSplit", logEvent.requestuniqueid);
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logServiceErrorZippedAndSplit' log: ", error);
    }
    
};

/**
    Uploads the large objects directly to S3 bucket
    Compress the dataObject object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param dataObject       (string / JSON / XML)   : large object that developer want to upload to S3
        @param requestuniqueid  (string)                : (optional) the unique-request-id for the large data-object
*/
const uploadObjectToS3 = async (global, serviceType, serviceName, message, dataObject, requestuniqueid) => {                // modified it to async
        try {
            const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "S3Upload");
            if (!dataObject) {
                throw new Error("dataObject is not present having message value '" + message + "'");
            }
            logEvent.dataObject = dataObject;
            logEvent.functionname = global.ctx.functionName || process.env.AWS_LAMBDA_FUNCTION_NAME;
            logEvent.awsRequestId = global.ctx.awsRequestId;
            logEvent.timestampInUnix = new Date(logEvent.timestamp).getTime();
            let uniqueId = v4();
            if (requestuniqueid && typeof requestuniqueid === "string") {
                uniqueId = requestuniqueid;
                logEvent.requestuniqueid = requestuniqueid;    
            } else if (requestuniqueid) {
                logEvent.requestuniqueid = strigifyObject(requestuniqueid, "uploadObjectToS3", requestuniqueid);
                consoleLoggerError("DBAAS LOGGING - Error in 'uploadObjectToS3' log, requestuniqueid is not of string type");
            } else {
                logEvent.requestuniqueid = "undefined";
            }
            const params = {
                Bucket: process.env.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME_LARGE_OBJECTS,
                Key: [
                    process.env.EXTENSION_LOG_DESTINATION_S3_BUCKET_NAME,
                    "processed",
                    "functionname=" + process.env.AWS_LAMBDA_FUNCTION_NAME,
                    "year=" + logEvent.timestamp.substr(0, 4),
                    "month=" + logEvent.timestamp.substr(5, 2),
                    "day=" + logEvent.timestamp.substr(8, 2),
                    global.ctx.awsRequestId + "#" + uniqueId + ".json"
                ].join("/"),
                Body: strigifyObject(logEvent, "uploadObjectToS3", logEvent.requestuniqueid)
            };
            const s3ObjectURI = "s3://" + params.Bucket + "/" + params.Key;
            const s3PutObjectCommand = new PutObjectCommand(params);
            try{
                const data = await s3Client.send(s3PutObjectCommand);
                if (data){
                    return new Promise((resolve, reject) => {
                    delete logEvent.dataObject;
                    logEvent.s3ObjectURI = s3ObjectURI;
                    console.log(strigifyObject(logEvent, "uploadObjectToS3", logEvent.requestuniqueid));
                    resolve(s3ObjectURI);
                    });
                }
            } catch(err) {
                return new Promise((resolve, reject) => {
            	console.error("DBAAS LOGGING - Error in s3PutObjectCommand having message value '" + message + "'", err, err.stack);
            	resolve();
            	});
            }
            
        } catch (error) {
            return new Promise((resolve, reject) => {
            consoleLoggerError("DBAAS LOGGING - Error in 'uploadObjectToS3' function: ", error);
            resolve();
            });
        }
};

/**
    Logs the completion message to console / kibana with status and response
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param status           (string)                : status of invocation, possible values - Success / Valid-Error / Exception
        @param response         (string / JSON / XML)   : final response of invocation
*/
const logCompletion = (global, serviceType, serviceName, message, status, response, statementLogLevel) => {
    try {
        const statusValues = ["Success", "Valid-Error", "Exception"];
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "Completion");
        if (status) {
            if (typeof status === "string") {
                if (statusValues.includes(status)) {
                    logEvent.status = status;
                } else {
                    logEvent.status = status;
                    consoleLoggerError("DBAAS LOGGING - Error in 'logCompletion' log, status value should be 'Success' or 'Valid-Error' or 'Exception'");
                }
            } else {
                logEvent.status = strigifyObject(status, "logCompletion");
                consoleLoggerError("DBAAS LOGGING - Error in 'logCompletion' log, status is not of string type");
            }
        } else {
            logEvent.status = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logCompletion' log, status is undefined");
        }
        logEvent.serviceResponse = response;
        if (shouldPrintConsole()) {
            const stringifiedObject = strigifyObject(logEvent, "logCompletion");
            if (isCompressionLoggingRequired(stringifiedObject)) {
                logCompletionZippedAndSplit(global, serviceType, serviceName, message, status, response, statementLogLevel);
            } else {
                const finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.info;
                printLogsWithSpecificLogLevel(finalLogLevel, stringifiedObject);
            }
        }
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logCompletion' log: ", error);
    }
};

/**
    Logs the completion message to console / kibana with status and response
    Compress the respone object
        @param global           (JSON Object)           : global object
        @param serviceType      (string)                : service type of lambda
        @param serviceName      (string)                : service name of lambda
        @param message          (string)                : message to log
        @param status           (string)                : status of invocation, possible values - Success / Valid-Error / Exception
        @param response         (string / JSON / XML)   : final response of invocation
*/
const logCompletionZippedAndSplit = (global, serviceType, serviceName, message, status, response, statementLogLevel) => {
    try {
        const statusValues = ["Success", "Valid-Error", "Exception"];
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "CompletionZippedAndSplit");
        if (status) {
            if (typeof status === "string") {
                if (statusValues.includes(status)) {
                    logEvent.status = status;
                } else {
                    logEvent.status = status;
                    consoleLoggerError("DBAAS LOGGING - Error in 'logCompletionZippedAndSplit' log, status value should be 'Success' or 'Valid-Error' or 'Exception'");
                }
            } else {
                logEvent.status = strigifyObject(status, "logCompletionZippedAndSplit");
                consoleLoggerError("DBAAS LOGGING - Error in 'logCompletionZippedAndSplit' log, status is not of string type");
            }
        } else {
            logEvent.status = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logCompletionZippedAndSplit' log, status is undefined");
        }
        logEvent.serviceResponse = response;
        if (shouldPrintConsole()) {
            let finalLogLevel = statementLogLevel?statementLogLevel:dbaasLogLevels.info;
            printZippedLogsWithSpecificLogLevel(finalLogLevel, logEvent, "serviceResponse", "logCompletionZippedAndSplit");
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logCompletionZippedAndSplit' log: ", error);
    }
};

/**
    Collects the particular invocation logs, zips them and send to Kinesis stream, if LAYER_ENABLED is true
        @param global   (JSON Object)  : global object
*/
const sendLogsToKinesis = async(global) => {
    if (isV3Enabled()) {
        return new Promise((resolve, reject) => {
            resolve();
        });
    } else {
            try {
                let logEventsArray = [];
                isObjectNotArray(global, "globalObject");
                validateGlobalObject(global);
                logEventsArray = lokiDB.find({ traceId: global.ctx.awsRequestId });
                const sendEvent = new Array();
                for (let i = 0; i < logEventsArray.length; i++) {
                    sendEvent.push(logEventsArray[i].data);
                    if (i == logEventsArray.length - 1) {
                        const strigifiedSendEvent = strigifyObject(sendEvent, "sendLogsToKinesis");
                        zlib.gzip(strigifiedSendEvent, async function(error, result) {    // use async function here
                            if (!error) {
                                consoleCritical('Zipped data to Kinesis: ' + result.toString('base64'));
                                const params = {
                                    Data: result,
                                    StreamName: dbaasLoggerConfig.streamName,
                                    PartitionKey: md5("DBaaS" + randomize('0', 4) * 100000),
                                    SequenceNumberForOrdering: '1'
                                };
                                const kinesisPutRecordCommand = new PutRecordCommand(params);
                                try{
                                    const data = await kinesisClient.send(kinesisPutRecordCommand);
                                    if (data){
                                        return new Promise((resolve, reject) => {
                                            console.log("putRecord", data);
                                            resolve(data);
                                        });
                                    }
                                } catch(err){
                                    return new Promise((resolve, reject) => {
                            		console.error("DBAAS LOGGING - Error in kinesisPutRecordCommand", err, err.stack); // an error occurred
                                    resolve();
                                    });
                            	}
                            } else {
                                return new Promise((resolve, reject) => {
                                console.error("DBAAS LOGGING - Error while zipping dbaaslogs: ", error);
                                resolve();
                                });
                            }
                        });
                    }
                }
            } catch (e) {
                return new Promise((resolve, reject) => {
                    console.error("DBAAS LOGGING - Error in 'sendLogsToKinesis' function: ", e);
                    resolve();
                });
            }
    }
};

const delay = (milliseconds = 2) => {
    const date = Date.now();
    let currentDate = null;
    do {
        currentDate = Date.now();
    } while (currentDate - date < milliseconds);
    return;
};

/**
    Compress and splits the data object and prints it inCloudWatch
        @param logEvent         (JSON Object)   : global object
        @param objectName       (string)        : name of object that needs to be zipped and split
        @param logType          (strin)         : function name from where it is called
        @param uniqueRequestId  (string)        : unique-request-id, if it is called from log service request/response/error methods
*/
function printZippedData(logEvent, objectName, logType, uniqueRequestId, printZippedLogs) {
    try {
        if (logEvent[objectName]) {
            const strigifiedLogeventObject = strigifyObject(logEvent[objectName], logType, uniqueRequestId);
            let zippedData;
            try {
                zippedData = zlib.gzipSync(strigifiedLogeventObject);
            } catch (zippedError) {
                console.error("DBAAS LOGGING - Error in compressing the data: ", zippedError);
                printZippedLogs(strigifyObject(logEvent, logType, uniqueRequestId));
                return;
            }
            
            zippedData = zippedData.toString('base64');
            consoleInfo("Data size after compression: " + zippedData.length);
            logEvent[objectName] = zippedData;
            logEvent["compressedFieldName"] = objectName;
            logEvent["isLogSplitted"] = false;
            logEvent["totalChunks"] = 1;
            logEvent["chunkSequenceNum"] = 1;
            logEvent["uniqueLogId"] = v4();
            
            const zippedDataSize = zippedData.length;
            const messageSize = strigifyObject(logEvent, logType, uniqueRequestId).length;
            const messageSizeLimit = (process.env.MESSAGE_SIZE_LIMIT || 50000) - (messageSize - zippedDataSize);
                    
            const totalMessageCount = Math.ceil(zippedDataSize / messageSizeLimit);
            logEvent["totalChunks"] = totalMessageCount;
            if (totalMessageCount > 1) {
                logEvent["isLogSplitted"] = true;
            }
                    
            for (let i = 1; i <= totalMessageCount; i++) {
                logEvent["chunkSequenceNum"] = i;
                logEvent[objectName] = zippedData.substring(messageSizeLimit * (i-1), messageSizeLimit*i);
                
                printZippedLogs(strigifyObject(logEvent, logType, uniqueRequestId));
                if (totalMessageCount > 1) {
                    delay(process.env.DELAY_IN_MS || 2);
                }
            }
        } else {
            printZippedLogs(strigifyObject(logEvent, logType, uniqueRequestId));
        }
    } catch (error) {
        console.error("DBAAS LOGGING - Error in printZippedData function: ", error);
    }
}

/**
    Returns the difference between 2 timestamps
        @param timestampreq   (string)  : timestamp of request log
        @param timestampres   (string)  : timestamp of response log
*/
function getDifference(timestampreq, timestampres) {
    try {
        const date = new Date(timestampreq.substr(0, timestampreq.length - 1));
        const date1 = new Date(timestampres.substr(0, timestampres.length - 1));
        const difference = Math.abs(date.getTime() - date1);
        return difference; 
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'getDifference' function: ", error);
        return 0;
    }
}


/**
    Logs the key-value pair
    @deprecated
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
*/
const logKey1 = (global, serviceType, serviceName, message, key, value) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "logKey1");
        if (key && (typeof key === "string" || typeof key === "number")) {
            logEvent.key1 = key;
        } else if (key) {
            logEvent.key1 = strigifyObject(key, "logKey1");
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey1' log, key is not of string type");
        } else {
            logEvent.key1 = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey1' log, key is undefined");
        }
        logEvent.value1 = value;
        if (shouldPrintConsole()) {
            console.log(strigifyObject(logEvent, "logKey1"));
        } 
        if(isV2Enabled()) {
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logKey1' log: ", error);
    }
};

/**
    Logs the key-value pair
    @deprecated
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
*/
const logKey2 = (global, serviceType, serviceName, message, key, value) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "logKey2");
        if (key && (typeof key === "string" || typeof key === "number")) {
            logEvent.key2 = key;
        } else if (key) {
            logEvent.key2 = strigifyObject(key, "logKey1");
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey2' log, key is not of string type");
        } else {
            logEvent.key2 = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey2' log, key is undefined");
        }
        logEvent.value2 = value;
        if (shouldPrintConsole()) {
            console.log(strigifyObject(logEvent, "logKey2"));
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logKey2' log: ", error);
    }
};

/**
    Logs the key-value pair
    @deprecated
        @param global           (JSON Object)   : global object
        @param serviceType      (string)        : service type of lambda
        @param serviceName      (string)        : service name of lambda
        @param message          (string)        : message to log
*/
const logKey3 = (global, serviceType, serviceName, message, key, value) => {
    try {
        const logEvent = getEventWithBasicInformationContextOnly(global, serviceType, serviceName, message, "logKey3");
        if (key && (typeof key === "string" || typeof key === "number")) {
            logEvent.key3 = key;
        } else if (key) {
            logEvent.key3 = strigifyObject(key, "logKey1");
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey3' log, key is not of string type");
        } else {
            logEvent.key3 = "undefined";
            consoleLoggerError("DBAAS LOGGING - Error in 'logKey3' log, key is undefined");
        }
        logEvent.value3 = value;
        if (shouldPrintConsole()) {
            console.log(strigifyObject(logEvent, "logKey3"));
        }  
        if(isV2Enabled()){
            lokiDB.insert({ traceId: global.ctx.awsRequestId, data: logEvent });
        }
    } catch (error) {
        consoleLoggerError("DBAAS LOGGING - Error in 'logKey3' log: ", error);
    }
};



/**
    To Encrypt the JSON Attriubte or XML tags or string value
        @param inputData           (JSON / Object / String) : inputData can be a JSON, xml or string formate
        @param attribute           (Array)                  : list of JSON attribute or xml tags which needs to be encrypted
        @param encryptionMethod    (string)                 : Type of encryption method
        @param inputDataType       (string)                 : "JSON" or "XML" or "String" 
*/
async function encryptData(inputData, attributes, encryptionMethod, inputDataType = "json") {
    try {
        inputDataType = inputDataType.toLowerCase();

        if (inputDataType == "xml") {
            let parsedXmlData = await xml2js.parseStringPromise(inputData);
            let encryptedParsedXmlData = await encryptXmlData(parsedXmlData, attributes, encryptionMethod);
            let encryptedXmlData = builder.buildObject(encryptedParsedXmlData);
            return encryptedXmlData;
        }
        else if (inputDataType == "json") {
            let encryptedJsonData = await encryptJsonData(inputData, attributes, encryptionMethod);
            return encryptedJsonData;
        }
        else {
            if(inputData) {
                let encryptedStringData = await encryptDataWithKMS(inputData, encryptionMethod);
                return encryptedStringData;
            } 
            
            return inputData;
        }
    }
    catch (err) {
        console.error("Error in encryptData Function: " + err);
    }
}

async function encryptJsonData(jsonData, attributes, encryptionMethod) {
    try {
        let ecnryptedAttribute = '';
        for (let key in jsonData) {

            if ((attributes.includes(key)) && ((typeof jsonData[key]) == "object")  && (jsonData[key])) {
                ecnryptedAttribute = await encryptJsonData(jsonData[key], Object.keys(jsonData[key]), encryptionMethod);
            } else if (!(attributes.includes(key)) && ((typeof jsonData[key]) == "object")) {
                await encryptJsonData(jsonData[key], attributes, encryptionMethod);
                continue;
            } else if ((attributes.includes(key)) && (typeof jsonData[key]) != "object" && (jsonData[key])) {
                ecnryptedAttribute = await encryptDataWithKMS(Buffer.from(jsonData[key].toString()), encryptionMethod);
            } else {
                continue;
            }

            if (Array.isArray(jsonData)) {
                jsonData[key] = ecnryptedAttribute;
            } else {
                delete jsonData[key]; 
                jsonData["#" + key] = ecnryptedAttribute;
            }
        }
        return jsonData;
    } catch (err) {
        console.error("Error in encryptJsonData Function: " + err);
    }
}

async function encryptXmlData(parsedXmlData, attributes, encryptionMethod) {
    try {
        let ecnryptedAttribute = '';
        for (let key in parsedXmlData) {
            if (key == "$") {
                continue;
            } else if ((attributes.includes(key)) && ((typeof parsedXmlData[key]) == "object") && (parsedXmlData[key])) {
                ecnryptedAttribute = await encryptXmlData(parsedXmlData[key], Object.keys(parsedXmlData[key]), encryptionMethod);
            } else if (!(attributes.includes(key)) && ((typeof parsedXmlData[key]) == "object")) {
                await encryptXmlData(parsedXmlData[key], attributes, encryptionMethod);
                continue;
            } else if ((attributes.includes(key)) && (typeof parsedXmlData[key]) != "object" && (parsedXmlData[key])) {

                ecnryptedAttribute = await encryptDataWithKMS(Buffer.from(parsedXmlData[key]), encryptionMethod);
            } else {
                continue;
            }

            if (Array.isArray(parsedXmlData)) {
                parsedXmlData[key] = ecnryptedAttribute;
            } else {
                delete parsedXmlData[key]; 
                parsedXmlData["_." + key] = ecnryptedAttribute;
            }
        }

        return parsedXmlData;
    } catch (err) {
        console.error("Error in encryptXmlData Function: " + err);
    }
}

async function encryptDataWithKMS(stringData, encryptionMethod) {
    try {
        if (encryptionMethod === "cmk-symmetric") {
            return await encryptDataWithCMKSymmetric(stringData);
        } else if (encryptionMethod === "cmk-asymmetric") {
            return await encryptDataWithCMKAsymmetric(stringData);
        } else if (encryptionMethod === "cmk-asymmetric-2048") {
            return await encryptDataWithCMKAsymmetric2048(stringData);
        } else if (encryptionMethod === "cmk-envelopesymmetric") {
            if (kmsGlobalData.encryption.encryptedDataKey) {
                return await encryptDataWithEnvelopeSymmetric(stringData, kmsGlobalData.encryption.encryptedDataKey, kmsGlobalData.encryption.unencryptedDataKey);
            } else {
                return await generateDataKeyCMKEnvelopeSymmetric(stringData);
            }
        }
    } catch (err) {
        console.error("Error in encryptDataWithKMS Function: " + err);
    }
}

async function encryptDataWithCMKSymmetric(stringData) {
        try {
            const params = {
                KeyId: process.env.CMK_SYMMETRIC_KEY_ARN,
                Plaintext: Buffer.from(stringData)
            };
            const kmsEncryptCommand = new EncryptCommand(params);
            try{
                const data = await kmsClient.send(kmsEncryptCommand);
                if(data){
                    return new Promise((resolve, reject) => {
                        resolve(Buffer.from(data.CiphertextBlob).toString('base64'));
                    });
                }
            } catch(err){
                return new Promise((resolve, reject) => {
                    console.log("Error in encryptDataWithCMKSymmetric->kmsEncryptCommand: ", err);
                    resolve("");
            });
            	}
        } catch (error) {
            console.error("Error in encryptDataWithCMKSymmetric function: " + error);
            }
}

async function encryptDataWithCMKAsymmetric(stringData) {
    try {
        const params = {
            KeyId: process.env.CMK_ASYMMETRIC_KEY_ARN,
            Plaintext: Buffer.from(stringData),
            EncryptionAlgorithm: "RSAES_OAEP_SHA_256"
        };
        const kmsEncryptCommand = new EncryptCommand(params);
        try{
            const data = await kmsClient.send(kmsEncryptCommand);
            if(data){
                return new Promise((resolve, reject) => {
            	    resolve(Buffer.from(data.CiphertextBlob).toString('base64'));
            	    });
                }
        } catch(err){
            return new Promise((resolve, reject) => {
                console.log("Error in encryptDataWithCMKAsymmetric->encrypt function: ", err);
                resolve("");
            });
        } 
    } catch (error) {
        console.error("Error in encryptDataWithCMKAsymmetric function: " + error);
    }
}

async function encryptDataWithCMKAsymmetric2048(stringData) {
    try {
        const params = {
            KeyId: process.env.CMK_ASYMMETRIC_2048_KEY_ARN,
            Plaintext: Buffer.from(stringData),
            EncryptionAlgorithm: "RSAES_OAEP_SHA_256"
        };
        const kmsEncryptCommand = new EncryptCommand(params);
        try{
            const data = await kmsClient.send(kmsEncryptCommand);
            if(data){
                return new Promise((resolve, reject) => {
                    resolve(Buffer.from(data.CiphertextBlob).toString('base64'));
                });
        	}
        } catch(err){
            return new Promise((resolve, reject) => {
        		console.log("Error in encryptDataWithCMKAsymmetric2048->encrypt function: ", err);
                resolve("");
        	});
        } 
    } catch(error) {
        console.error("Error in encryptDataWithCMKAsymmetric2048 function: " + error);
        }
}

async function generateDataKeyCMKEnvelopeSymmetric(stringData) {
        try {
            const params = {
                KeyId: process.env.CMK_ENVELOPE_SYMMETRIC_KEY_ARN,
                KeySpec: "AES_256"
            };
            const kmsGenerateDataKeyCommand = new GenerateDataKeyCommand(params);
            try{
                const data = await kmsClient.send(kmsGenerateDataKeyCommand);
                kmsGlobalData.encryption.encryptedDataKey = Buffer.from(data.CiphertextBlob).toString("base64");
                kmsGlobalData.encryption.unencryptedDataKey = Buffer.from(data.Plaintext).toString("base64");
                
                if (data){
                    return new Promise((resolve, reject) => {
                        resolve(encryptDataWithEnvelopeSymmetric(stringData, kmsGlobalData.encryption.encryptedDataKey, kmsGlobalData.encryption.unencryptedDataKey));
                    });
                }
            }catch(err){
                    return new Promise((resolve, reject) => {
                    console.log("Error in generateDataKeyCMKEnvelopeSymmetric->generateDataKey function: ", err);
                    resolve("");
                    });
            }
                
    }catch (error) {
            console.error("Error in generateDataKeyCMKEnvelopeSymmetric function: " + error);
        }
}

function encryptDataWithEnvelopeSymmetric(stringData, encryptedDataKey, unencryptedDataKey) {
    try {
        const algorithm = "AES-256-CBC";
        const iv = crypto.randomBytes(16);
        let encryptor = crypto.createCipheriv(algorithm, Buffer.from(unencryptedDataKey, "base64"), iv);
        encryptor.write(Buffer.from(stringData, "utf-8"));
        encryptor.end();
        
        const encryptedDataWithEncryptedDataKey = {};
        encryptedDataWithEncryptedDataKey["encryptedDataKey"] = encryptedDataKey;
        encryptedDataWithEncryptedDataKey["encryptedData"] = iv.toString('base64') + ":" +encryptor.read().toString('base64');
        
        return JSON.stringify(encryptedDataWithEncryptedDataKey);
    } catch (error) {
        console.error("Error in encryptDataWithEnvelopeSymmetric function: " + error);
    }
}

const replacer = (key, value) =>
  typeof value === 'undefined' ? "undefinedValue" : value;

/**
    To Decrypt the JSON Attriubte or XML tags or string data
        @param inputEncryptedData  (JSON Object or String) : inputEncryptedData can be a JSON, xml or string formate with encrypted data.
        @param decryptionMethod    (string)                : Type of encryption method
        @param inputDataType       (string)                : "JSON" or "XML" or "String" 
*/
async function decryptData(inputEncryptedData, decryptionMethod, inputDataType) {
    try {
        if (!(inputDataType))
            inputDataType = findInputDataType(inputEncryptedData);

        inputDataType = inputDataType.toLowerCase();

        if (inputDataType == "xml") {
            let parsedEncryptedXmlData = await xml2js.parseStringPromise(inputEncryptedData);
            let decryptedParsedXmlData = await decryptXmlData(parsedEncryptedXmlData, decryptionMethod, 0);
            let decryptedXmlData = builder.buildObject(decryptedParsedXmlData);
            return JSON.stringify(decryptedXmlData);
        }
        else if (inputDataType == "json") {
            let decryptedJsonData = await decryptJsonData(inputEncryptedData, decryptionMethod, 0);
            return JSON.stringify(decryptedJsonData, replacer);
        }
        else {
            //string handling
            let decryptedStringData = await decryptDataWithKMS(inputEncryptedData, decryptionMethod);
            return JSON.stringify(decryptedStringData);
        }
    }
    catch (err) {
        console.error(err);
    }
}

async function decryptJsonData(jsonData, decryptionMethod, isParentEncryption = 0) {

    try {
        let decryptedAttribute = '';
        for (let key in jsonData) {
            if ((key[0] == "#") && (typeof jsonData[key]) == "object") {
                decryptedAttribute = await decryptJsonData(jsonData[key], decryptionMethod, 1);
            }
            else if ((key[0] != "#") && (typeof jsonData[key]) == "object") {
                decryptedAttribute = await decryptJsonData(jsonData[key], decryptionMethod, 0);
            }
            else if (((key[0] == "#") || isParentEncryption == 1) && (typeof jsonData[key]) == "string") {
                decryptedAttribute = await decryptDataWithKMS(jsonData[key], decryptionMethod);
            }
            else {
                continue;
            }

            if (Array.isArray(jsonData)) {
                jsonData[key] = decryptedAttribute;
            }
            else {
                delete jsonData[key]; 
                jsonData[key.replace('#', '')] = decryptedAttribute;
            }
        }

        return jsonData;
    }
    catch (err) {
        console.error("Error in decryptData Function: " + err);
    }
}

async function decryptXmlData(xmlData, decryptionMethod, isParentEncryption = 0) {

    try {
        let decryptedAttribute = '';

        for (let key in xmlData) {

            if (key == "$") {
                continue;
            }
            else if ((key[0] == "_" && key[1] == '.') && (typeof xmlData[key]) == "object") {
                decryptedAttribute = await decryptXmlData(xmlData[key], decryptionMethod, 1);
            }
            else if (!(key[0] == "_" && key[1] == '.') && (typeof xmlData[key]) == "object") {
                await decryptXmlData(xmlData[key], decryptionMethod, 0);
                continue;
            }
            else if (((key[0] == "_" && key[1] == '.') || isParentEncryption == 1) &&
                (typeof xmlData[key]) == "string") {
                decryptedAttribute = await decryptDataWithKMS(xmlData[key], decryptionMethod);
            }
            else {
                continue;
            }

            if (Array.isArray(xmlData)) {
                xmlData[key] = decryptedAttribute;
            }
            else {
                delete xmlData[key]; 
                xmlData[key.replace('_.', '')] = decryptedAttribute;
            }
        }
        return xmlData;
    }
    catch (err) {
        console.error("Error in decryptXmlData Function: " + err);
    }
}

function findInputDataType(inputData) {
    try {
        if (typeof inputData == "object") {
            return 'json';
        }
        else if (inputData.includes('<?xml version="1.0" encoding="UTF-8" standalone="yes"?>')) {
            return 'xml';
        }
        else {
            return 'string';
        }
    }
    catch (err) {
        console.error("Error in findInputDataType Function: " + err);
    }
}

async function decryptDataWithKMS(encrytedStringData, decryptionMethod) {
    try {
        if (decryptionMethod === "cmk-symmetric") {
            return await decryptDataWithCMKSymmetric(encrytedStringData);
        } else if (decryptionMethod === "cmk-asymmetric") {
            return await decryptDataWithCMKAsymmetric(encrytedStringData);
        } else if (decryptionMethod === "cmk-asymmetric-2048") {
            return await decryptDataWithCMKAsymmetric2048(encrytedStringData);
        } else if (decryptionMethod === "cmk-envelopesymmetric") {
            const encryptedPrivateKey = JSON.parse(encrytedStringData)['encryptedDataKey'];
            const encryptedData = JSON.parse(encrytedStringData)['encryptedData'];
            if (kmsGlobalData.decryption.unencryptedDataKey) {
                if (kmsGlobalData.decryption.encryptedDataKey === encryptedPrivateKey) {
                    return await decryptDataWithEnvelopeSymmetric(encryptedData, kmsGlobalData.decryption.unencryptedDataKey);
                }
            }
            return await decryptDataKeyWithCMKEnvelopeSymmetric(encryptedData, encryptedPrivateKey);
        }
    } catch (err) {
        console.error("Error in decryptDataWithKMS Function: " + err);
    }
}

async function decryptDataWithCMKSymmetric(encrytedStringData) {
    try {
        const params = {
            KeyId: process.env.CMK_SYMMETRIC_KEY_ARN,
            CiphertextBlob: Buffer.from(encrytedStringData, 'base64')
        };
        const kmsDecryptCommand = new DecryptCommand(params);
        try{
            const data = await kmsClient.send(kmsDecryptCommand);
            if(data){
                return new Promise((resolve, reject) => {
                    resolve(Buffer.from(data.Plaintext).toString('utf8'));
                });
            }
        } catch(err){
            return new Promise((resolve, reject) => {
                console.log("Error in decryptDataWithCMKSymmetric->KmsDecryptCommand: ", err);
                resolve("");
            });
        }
    } catch (error) {
        console.error("Error in decryptDataWithCMKSymmetric function: " + error);
    }
}

async function decryptDataWithCMKAsymmetric(encrytedStringData) {
    try {
        const params = {
            KeyId: process.env.CMK_ASYMMETRIC_KEY_ARN,
            CiphertextBlob: Buffer.from(encrytedStringData, 'base64'),
            EncryptionAlgorithm: "RSAES_OAEP_SHA_256"
        };
        const kmsDecryptCommand = new DecryptCommand(params);
        try{
            const data = await kmsClient.send(kmsDecryptCommand);
            if(data){
                    return new Promise((resolve, reject) => {
                        resolve(Buffer.from(data.Plaintext).toString('utf8'));
                });
            }
        } catch(err){
            return new Promise((resolve, reject) => {
                console.log("Error in decryptDataWithCMKAsymmetric->kmsDecryptCommand: ", err);
                resolve("");
            });
        }
                
    } catch(error) {
            console.error("Error in decryptDataWithCMKAsymmetric function: " + error);
        }
}

async function decryptDataWithCMKAsymmetric2048(encrytedStringData) {
    try {
        const params = {
            KeyId: process.env.CMK_ASYMMETRIC_2048_KEY_ARN,
            CiphertextBlob: Buffer.from(encrytedStringData, 'base64'),
            EncryptionAlgorithm: "RSAES_OAEP_SHA_256"
        };
        const kmsDecryptCommand = new DecryptCommand(params);
        try{
            const data = await kmsClient.send(kmsDecryptCommand)
            if(data){
                return new Promise((resolve, reject) => {
                    resolve(data.Plaintext.toString('utf8'));
                });
            }
        } catch(err){
            return new Promise((resolve, reject) => {
                console.log("Error in decryptDataWithCMKAsymmetric2048->kmsdecryptCommand: ", err);
                resolve("");
            });
        }
    } catch (error) {
        console.error("Error in decryptDataWithCMKAsymmetric2048 function: " + error);
        }
}

async function decryptDataKeyWithCMKEnvelopeSymmetric(encrytedStringData, encryptedPrivateKey) {
    try {
        const params = {
            KeyId: process.env.CMK_ENVELOPE_SYMMETRIC_KEY_ARN,
            CiphertextBlob: Buffer.from(encryptedPrivateKey, 'base64')
        };
        const kmsDecryptCommand = new DecryptCommand(params);
        try {
            const data = await kmsClient.send(kmsDecryptCommand);
            if (data){
                return new Promise((resolve, reject) => {
                    kmsGlobalData.decryption.unencryptedDataKey = Buffer.from(data.Plaintext).toString('base64');
                    resolve(decryptDataWithEnvelopeSymmetric(encrytedStringData, kmsGlobalData.decryption.unencryptedDataKey));
                });
            }
        }catch(err){
            return new Promise((resolve, reject) => {
                console.log("Error in decryptDataKeyWithCMKEnvelopeSymmetric kmsdecryptCommand: ", err);
                resolve("");
            });
        }
    } catch (error) {
        console.error("Error in decryptDataKeyWithCMKEnvelopeSymmetric function: " + error);
    }
}

function decryptDataWithEnvelopeSymmetric(encrytedStringData, unencryptedPrivateKey) {
    try {
        const algorithm = "AES-256-CBC";
        const encrytedStringDataParts = encrytedStringData.split(":");
        const iv = Buffer.from(encrytedStringDataParts.shift(), 'base64');
        const encrytedData = encrytedStringDataParts.join(":");
        let decryptor = crypto.createDecipheriv(algorithm, Buffer.from(unencryptedPrivateKey, "base64"), iv);
        decryptor.write(Buffer.from(encrytedData, 'base64'));
        decryptor.end();
        
        return decryptor.read().toString('utf-8');
    }
    catch (error) {
        console.error("Error in decryptDataWithEnvelopeSymmetric function: " + error);
    }
}

/**
    To Mask the JSON Attriubte or XML tags or string value
        @param inputData            (JSON / Object / String) : inputData can be a JSON or xml or string format
        @param attributes           (Array)                  : list of JSON attribute or xml tags which needs to be encrypted
        @param unmaskPrefixChars    (number)                 : number of characters that need to be unmasked from start of the string,if not given default is 2
        @param unmaskSuffixChars    (number)                 : number of characters that need to be unmasked from end of the string,if not given default is 2 
        @param maskWithChar         (string (single char))   : character using which msking should we performed, if not given default is '*' 
        @param inputDataType        (string)                 : "JSON" or "XML" or "String" ,if not default is JSON
*/

function maskData(inputData, attributes, unmaskPrefixChars=2, unmaskSuffixChars=2,maskWithChar='*', inputDataType = "json") {
    try {
        inputDataType = inputDataType.toLowerCase();
        const clonedObject=JSON.parse(JSON.stringify(inputData));
        if (inputDataType == "xml") {
            let parsedXmlData = xml2js.parseStringPromise(clonedObject);
            let maskedParsedXmlData = maskXmlData(parsedXmlData, attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            let maskedXmlData = builder.buildObject(maskedParsedXmlData);
            return maskedXmlData;
        }
        else if (inputDataType == "json") {
            let maskedJsonData = maskJsonData(clonedObject, attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            return maskedJsonData;
        }
        else {
            if (clonedObject) {
                let maskedStringData = maskDataForAttribute(clonedObject, unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
                return maskedStringData;
            }

            return clonedObject;
        }
    }
    catch (err) {
        console.error("Error in maskData Function: " + err);
    }
}

function maskJsonData(jsonData, attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar) {
    try {
        let maskedAttribute = '';
        for (let key in jsonData) {

            if ((attributes.includes(key)) && ((typeof jsonData[key]) == "object") && (jsonData[key])) {
                maskedAttribute = maskJsonData(jsonData[key], Object.keys(jsonData[key]), unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            } else if (!(attributes.includes(key)) && ((typeof jsonData[key]) == "object")) {
                maskJsonData(jsonData[key], attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
                continue;
            } else if ((attributes.includes(key)) && (typeof jsonData[key]) != "object" && (jsonData[key])) {
                maskedAttribute = maskDataForAttribute(jsonData[key].toString(), unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            } else {
                continue;
            }

            if (Array.isArray(jsonData)) {
                jsonData[key] = maskedAttribute;
            } else {
                delete jsonData[key];
                jsonData["#" + key] = maskedAttribute;
            }
        }
        return jsonData;
    } catch (err) {
        console.error("Error in maskJsonData Function: " + err);
    }
}

function maskXmlData(parsedXmlData, attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar) {
    try {
        let maskedAttribute = '';
        for (let key in parsedXmlData) {
            if (key == "$") {
                continue;
            } else if ((attributes.includes(key)) && ((typeof parsedXmlData[key]) == "object") && (parsedXmlData[key])) {
                maskedAttribute = maskXmlData(parsedXmlData[key], Object.keys(parsedXmlData[key]), unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            } else if (!(attributes.includes(key)) && ((typeof parsedXmlData[key]) == "object")) {
                maskXmlData(parsedXmlData[key], attributes, unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
                continue;
            } else if ((attributes.includes(key)) && (typeof parsedXmlData[key]) != "object" && (parsedXmlData[key])) {
                maskedAttribute = maskDataForAttribute(parsedXmlData[key], unmaskPrefixChars, unmaskSuffixChars,maskWithChar);
            } else {
                continue;
            }

            if (Array.isArray(parsedXmlData)) {
                parsedXmlData[key] = maskedAttribute;
            } else {
                delete parsedXmlData[key];
                parsedXmlData["_." + key] = maskedAttribute;
            }
        }

        return parsedXmlData;
    } catch (err) {
        console.error("Error in maskXmlData Function: " + err);
    }
}

function maskDataForAttribute(data, unmaskPrefixChars, unmaskSuffixChars, maskWithChar) {
    const defaultStringMaskV2Options = {
        maskWith: maskWithChar,
        maxMaskedCharacters: 256,
        unmaskedStartCharacters: unmaskPrefixChars,
        unmaskedEndCharacters: unmaskSuffixChars
    };
    data = mask.maskStringV2(data, defaultStringMaskV2Options);
    return data;
}

function sendToSentinelS3(global,s3Bucket, dataObject) {
    return new Promise((resolve,reject) => {
        try {
            const d = new Date();
            const year = d.getFullYear();
            const month = d.getMonth()+1;
            const day = d.getDate();
            const functionname = process.env.AWS_LAMBDA_FUNCTION_NAME
            if(s3Bucket && dataObject){
                const params={
                    Bucket: s3Bucket,
                    Key: [
                        "sentinelJsonObject",
                        "functionname=" + functionname,
                        "year=" + year,
                        "month=" + month,
                        "day=" + day,
                        functionname+"#"+day+month+year+"#"+global.ctx.awsRequestId+".json"
                    ].join("/"),
                    Body: JSON.stringify(dataObject)
                };
                console.log("Params: "+JSON.stringify(params))
                const s3PutObjectCommand = new PutObjectCommand(params);    
                s3Client.send(s3PutObjectCommand,async (err,data)=>{
                    if(err) {
                        console.error('DBAAS LOGGING - Error in sentinel s3PutObjectCommand sdk: ',err);
                        reject(err);
                    } else {
                        console.log("DBAAS LOGGING - Message send to sentinel s3 with response: ",response);
                        resolve(data);
                    }
                });
            } else {
                console.log("DBAAS LOGGING - S3 Bucket name variable or DataObject is not valid!");
                reject();
            }
        } catch(err) {
            console.error("DBAAS LOGGING - Error in put object  input" + JSON.stringify(dataObject) + "'", err, err.stack);
        }
    });
};

function sendToSqs(queueUrl, dataObject) {
    return new Promise((resolve,reject) => {
        try {
            if(queueUrl && dataObject){
                const input={
                    QueueUrl: queueUrl,
                    MessageBody: JSON.stringify(dataObject)
                };
                const command = new SendMessageCommand(input);
                sqsClient.send(command, async (err, data) => {
                    if(err) {
                        console.error('DBAAS LOGGING - Error in SQS send message sdk: ',err);
                        reject(err);
                    } else {
                        console.log("DBAAS LOGGING - Message send to SQS with response: ",response);
                        resolve(response);
                    }
                });
            } else {
                console.log("DBAAS LOGGING - Sqs Url variable or DataObject is not valid!");
                reject();
            }
        } catch(err) {
            console.error("DBAAS LOGGING - Error in send SQS message having sqs input" + JSON.stringify(dataObject) + "'", err, err.stack);
        }
    });
};
module.exports = {
    start,
    logServiceRequest,
    logServiceResponse,
    logMessage,
    logServiceError,
    logMessageToSqs,
    logKey1,
    logKey2,
    logKey3,
    logCompletion,
    addFunctionalKeys,
    logSecretInformation,
    sendLogsToKinesis,
    dbaasLoggerConfig,
    uploadObjectToS3,
    dbaasLogLevels,
    encryptData,
    decryptData,
    maskData,
    sendObjectToSentinelS3
};
