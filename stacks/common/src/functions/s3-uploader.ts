const { LOGGER, flowUtil } = require("../utils/");
const {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
} = require("@aws-sdk/client-s3");
const fastcsv = require("fast-csv");
const { SNSClient, PublishCommand } = require("@aws-sdk/client-sns");

const WEBFOCUS_S3_BUCKET = process.env.WEBFOCUS_S3_BUCKET;
const VENDOR_GENERATING_FILE_PATH = process.env.VENDOR_GENERATING_FILE_PATH;
const VENDOR_UPLOADING_FILE_PATH = process.env.VENDOR_UPLOADING_FILE_PATH;
const WO_GENERATING_FILE_PATH = process.env.WO_GENERATING_FILE_PATH;
const WO_UPLOADING_FILE_PATH = process.env.WO_UPLOADING_FILE_PATH;
const PO_GENERATING_FILE_PATH = process.env.PO_GENERATING_FILE_PATH;
const PO_UPLOADING_FILE_PATH = process.env.PO_UPLOADING_FILE_PATH;
const PROCESSED_FILE_GENERATING_PATH =
  process.env.PROCESSED_FILE_GENERATING_PATH;

const handler = async (event, context) => {
  flowUtil.initiateGenericFlow(event, context, LOGGER.constants.SERVICE_NAME);
  LOGGER.start(event, "Trigger s3 uploader lambda");

  try {
    const triggeringBucket = event?.detail?.bucket?.name;
    const triggeringObjectKey = event?.detail?.object.key;
    const fileName = triggeringObjectKey.split("/").pop();
    const fileNamePrefix =
      triggeringObjectKey.split("/").slice(0, -2).join("/") + "/";

    await validateCSVFile(triggeringBucket, triggeringObjectKey);

    let uploadingFolderPath;
    switch (fileNamePrefix) {
      case VENDOR_GENERATING_FILE_PATH:
        uploadingFolderPath = VENDOR_UPLOADING_FILE_PATH;
        break;
      case WO_GENERATING_FILE_PATH:
        uploadingFolderPath = WO_UPLOADING_FILE_PATH;
        break;
      case PO_GENERATING_FILE_PATH:
        uploadingFolderPath = PO_UPLOADING_FILE_PATH;
        break;
    }

    const uploadingBucketName = WEBFOCUS_S3_BUCKET;

    LOGGER.info(
      `Copying file ${triggeringObjectKey} from ${triggeringBucket} to ${uploadingFolderPath} in ${uploadingBucketName}`
    );

    const s3Client = new S3Client({});

    const command = new CopyObjectCommand({
      Bucket: uploadingBucketName,
      CopySource: `${triggeringBucket}/${triggeringObjectKey}`,
      Key: `${uploadingFolderPath}${fileName}`,
    });

    const data = await s3Client.send(command);

    // movile to Processed folder after uploading to target bucket
    if (data.$metadata.httpStatusCode === 200) {
      const copyComman = new CopyObjectCommand({
        Bucket: triggeringBucket,
        CopySource: `${triggeringBucket}/${triggeringObjectKey}`,
        Key: `${fileNamePrefix}${PROCESSED_FILE_GENERATING_PATH}${fileName}`,
      });
      await s3Client.send(copyComman);

      const deleteCommand = new DeleteObjectCommand({
        Bucket: triggeringBucket,
        Key: triggeringObjectKey,
      });
      await s3Client.send(deleteCommand);
    }
    LOGGER.success("S3 object copied successfully");
  } catch (error) {
    LOGGER.error(error, "Error happen while uploading into webfocus bucket");
    await publishMessage({
      Message: error.message,
      Subject: "Error uploading file to webfocus bucket",
      TopicArn: process.env.SNS_TOPIC_ARN,
    });
  }
};

const validateCSVFile = async (triggeringBucket, triggeringObjectKey) => {
  try {
    const s3Client = new S3Client({});
    const getCommand = new GetObjectCommand({
      Bucket: triggeringBucket,
      Key: triggeringObjectKey,
    });
    const { Body } = await s3Client.send(getCommand);

    if (!Body) {
      throw new Error(
        `Unable to find the file in the S3 bucket: ${triggeringObjectKey}`
      );
    }

    const jsonCSV = await parseCSV(Body);
    LOGGER.info(`Data extracted from csv: ${jsonCSV.length}`);
    if (jsonCSV.length == 0) {
      throw new Error(`File is empty ${triggeringObjectKey}`);
    }
    LOGGER.info(`Succesfuly validate the file: ${triggeringObjectKey}`);
  } catch (error) {
    LOGGER.error(error, "Error happen while validating csv file");
    throw error;
  }
};

const parseCSV = async (responseBody) => {
  const csvContent = await new Promise((resolve, reject) => {
    let data = "";
    responseBody.on("data", (chunk) => (data += chunk));
    responseBody.on("end", () => resolve(data));
    responseBody.on("error", (err) => reject(err));
  });

  return new Promise((resolve, reject) => {
    try {
      const data = [];
      fastcsv
        .parseString(csvContent)
        .on("data", (raw) => {
          data.push(raw);
        })
        .on("end", (rawCount) => {
          LOGGER.info(`Data extracted from csv raw count: ${rawCount}`);
          resolve(data);
        })
        .on("error", (error) => reject(error));
    } catch (error) {
      reject(error);
    }
  });
};

const publishMessage = async (params) => {
  const sns = new SNSClient({});
  const command = new PublishCommand(params);
  return sns.send(command);
};

module.exports = {
  handler,
};
