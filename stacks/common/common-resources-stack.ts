import * as cdk from "aws-cdk-lib";
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from "constructs";

import { DLQ } from "./resources/sqs/dlq";
import { OAuthRefreshStackProps } from "./src/interfaces/types.interface";
import { RefreshTokenLambdaStack } from "./resources/lambda/refreshTokenLambda";
import { RefreshTokenLambdaAlarms } from "./resources/cloudwatch/alarms";
import { addRequiredTags } from "./src/utils/tags";

export class OAuthRefreshStack extends cdk.Stack {

  constructor(scope: Construct, id: string, props: OAuthRefreshStackProps) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: props.config.stackName,
      ...props,
      synthesizer: props.synthesizer
    });

    // Instantiate resources
    const vpc = ec2.Vpc.fromLookup(this, 'VPC', {
      vpcId: props.config.vpc.vpcId,
      isDefault: false
    });
    
    const securityGroups = new ec2.SecurityGroup(this, "PNRStoreSG", {
      vpc,
      securityGroupName: props.config.vpc.securityGroupsName,
      description: "PNR-store SG for Lambda ENI to poll MSK topic",
      allowAllOutbound: false,
    });

  }
}