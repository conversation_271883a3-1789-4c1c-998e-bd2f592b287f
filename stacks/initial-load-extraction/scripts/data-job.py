import sys
import os
import logging
import json
import boto3
from datetime import datetime
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from awsglue.dynamicframe import DynamicFrame
# Import glue-utils
from glue_utils.core.glue_context_wrapper import GlueContextWrapper

if "pytest" in sys.modules:
    sys.argv = [
        "",
        "--JOB_NAME=initial-load-extraction-job",
        "--DISTRIBUTION_BUCKET_NAME=mocktest",
        "--REGION=mocktest",
        "--ACCOUNT_ID=mocktest"
    ]

#initialize jobs and context
args = getResolvedOptions(sys.argv, ["JOB_NAME","DISTRIBUTION_BUCKET_NAME","REGION","ACCOUNT_ID","TRAX_DB_SECRET","START_DATE","END_DATE","DISTRIBUTION_FOLDER_PATH_WO","FILE_PREFIX_WO","DISTRIBUTION_FOLDER_PATH_VENDOR","FILE_PREFIX_VENDOR","DISTRIBUTION_FOLDER_PATH_PO","FILE_PREFIX_PO","SNS_TOPIC_ARN","PENDING_FILE_GENERATING_PATH"])
# Initialize GlueContextWrapper instead of standard context
glue_wrapper = GlueContextWrapper()
spark = glue_wrapper.spark
sc = spark.sparkContext
glueContext = GlueContext(sc)
job = Job(glueContext)

JOB_NAME = args['JOB_NAME']
JOB_RUN_ID = args['JOB_RUN_ID']
DISTRIBUTION_BUCKET_NAME = args['DISTRIBUTION_BUCKET_NAME']
REGION = args['REGION']
ACCOUNT_ID = args['ACCOUNT_ID']
TRAX_DB_SECRET = args['TRAX_DB_SECRET']
START_DATE = args['START_DATE']
END_DATE = args['END_DATE']
DISTRIBUTION_FOLDER_PATH_WO = args['DISTRIBUTION_FOLDER_PATH_WO']
FILE_PREFIX_WO = args['FILE_PREFIX_WO']
DISTRIBUTION_FOLDER_PATH_VENDOR = args['DISTRIBUTION_FOLDER_PATH_VENDOR']
FILE_PREFIX_VENDOR = args['FILE_PREFIX_VENDOR']
DISTRIBUTION_FOLDER_PATH_PO = args['DISTRIBUTION_FOLDER_PATH_PO']
FILE_PREFIX_PO = args['FILE_PREFIX_PO']
SNS_TOPIC_ARN = args['SNS_TOPIC_ARN']
PENDING_FILE_GENERATING_PATH = args['PENDING_FILE_GENERATING_PATH']
#Config logging for system
logging.basicConfig(level=os.environ.get("LOGLEVEL", "INFO"),
    format='[%(filename)s:%(lineno)s - %(funcName)10s() ] %(asctime)-15s %(message)s',
    datefmt='%Y-%m-%d:%H:%M:%S')
logger = logging.getLogger(__name__)

def send_error_to_sns(ex):
    sns = boto3.client('sns', region_name=REGION)
    sns.publish(
        TopicArn=SNS_TOPIC_ARN,
        Message=str(ex),
        Subject=f'Error From Glue Job {JOB_NAME}'
    )

def get_trax_db_credentials():
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=REGION);
    response = client.get_secret_value(SecretId=TRAX_DB_SECRET)
    secretString = response['SecretString']
    secret = json.loads(secretString)
    jdbc_username = secret['username']
    jdbc_password = secret['password']
    host = secret['connectString']
    jdbc_url = f"jdbc:oracle:thin:@{host}"

    return jdbc_username, jdbc_password, jdbc_url

def get_yearly_date_pairs(start_date_str, end_date_str):
    # Parse input dates
    start_date = datetime.strptime(start_date_str, '%Y%m%d')
    end_date = datetime.strptime(end_date_str, '%Y%m%d')
    
    result = []
    current_year = start_date.year
    
    while current_year <= end_date.year:
        # Determine start date for current year
        if current_year == start_date.year:
            year_start = start_date.strftime('%Y-%m-%d')
        else:
            year_start = f"{current_year}-01-01"
        
        # Determine end date for current year
        if current_year == end_date.year:
            year_end = end_date.strftime('%Y-%m-%d')
        else:
            year_end = f"{current_year}-12-31"
        
        result.append({
            'start_date': year_start,
            'end_date': year_end
        })
        
        current_year += 1
    
    return result

def renamePySparkS3FileName(bucketName, folderPath, newFileName):
    #Rename s3 file to specific custom name    
    client = boto3.client('s3')
    #getting all the content/file inside the bucket specific folder
    response = client.list_objects_v2(Bucket=bucketName, Prefix=folderPath)
    names = response["Contents"]
    
    #Find out the file which have part-000* in it's Key
    particulars = [name['Key'] for name in names if 'part-000' in name['Key']]
    
    #Find out the prefix of part-000* because we want to retain the partitions schema 
    location = [particular.split('part-000')[0] for particular in particulars]
    
    #copy object with custom name and delete old object
    for key,particular in enumerate(particulars):
        client.copy_object(Bucket=bucketName, CopySource=bucketName + "/" + particular, Key=location[key]+newFileName)
        client.delete_object(Bucket=bucketName, Key=particular)

def put_file_to_s3(df, folder_path,file_prefix,start_date,end_date):
    folder_path = folder_path + PENDING_FILE_GENERATING_PATH
    file_path = f"s3://{DISTRIBUTION_BUCKET_NAME}/{folder_path}/"
    glue_wrapper.write_data(
        dataframe=df.coalesce(1),
        target_type="S3",
        connection_options={
            "path": file_path,
            "format": "csv",
            "delimiter": ",",
            "header": "true",
            "escape": "\"",
        },
        mode="append"
    )
    current_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if start_date is None and end_date is None:
        file_name = f"{file_prefix}_{current_timestamp}.csv"
    else:
        file_name = f"{file_prefix}_{start_date}_{end_date}_{current_timestamp}.csv"    
    
    renamePySparkS3FileName(DISTRIBUTION_BUCKET_NAME, folder_path, file_name)

def get_vendor_data(jdbc_url, jdbc_username, jdbc_password):
    logger.info(f"Started getting vendor data")

    df_vendor = glue_wrapper.load_data(
        source_type="JDBC",
        use_catalog=False,
        connection_options={
            "url": jdbc_url,
            "user": jdbc_username,
            "password": jdbc_password,
            "dbtable": "(SELECT * FROM ODB.RELATION_MASTER WHERE RELATION_TRANSACTION='VENDOR') filtered_data",
            "disableUrlUpdate": "true", 
            "zeroDateTimeBehavior": "convertToNull"
        }
    )
    df_vendor.createOrReplaceTempView("RELATION_MASTER")

    sql_query = """
    SELECT 
        RELATION_CODE, 
        NAME,
        MAIL_EMAIL,
        MAIL_PHONE,
        MAIL_ADDRESS_1,
        MAIL_ADDRESS_2,
        MAIL_CITY,
        MAIL_STATE,
        MAIL_COUNTRY,
        MAIL_POST
    FROM RELATION_MASTER
    """

    finalDf = glue_wrapper.execute_spark_sql(sql_query)
    finalDf.cache() # Cache the result for better performance

    if not finalDf.isEmpty():
        put_file_to_s3(finalDf, DISTRIBUTION_FOLDER_PATH_VENDOR, FILE_PREFIX_VENDOR, None, None)
        logger.info("Vendor data written to S3 successfully")
    else:
        logger.info('No Vendor records to write') 

def get_wo_and_po_data(start_date, end_date, jdbc_url, jdbc_username, jdbc_password):
    logger.info(f"Started getting WO and PO data from {start_date} to {end_date}")

    # Process WO data with complex query equivalent to Oracle SQL
    wo_sql_query = f"""
    WITH WO_INIT AS (
        SELECT
            WO.WO,
            WO.AC,
            WO.AC_TYPE,
            WO.PROJECT,
            WO.EXTERNAL_REFERENCE,
            WO.WO_CATEGORY,
            WO.PRIORITY,
            WO.STATUS,
            WO.LOCATION,
            WO.VENDOR,
            WO.SITE,
            REPLACE (
                REPLACE (WO.WO_DESCRIPTION, '"', ' '),
                '&',
                'and'
            ) AS WO_DESCRIPTION,
            WO.SCHEDULE_ORG_COMPLETION_DATE,
            WO.SCHEDULE_ORG_COMPLETION_HOUR,
            WO.SCHEDULE_ORG_COMPLETION_MINUTE,
            WO.SCHEDULE_COMPLETION_DATE,
            WO.SCHEDULE_COMPLETION_HOUR,
            WO.SCHEDULE_COMPLETION_MINUTE,
            WO.SCHEDULE_START_DATE,
            WO.SCHEDULE_START_HOUR,
            WO.SCHEDULE_START_MINUTE,
            WO.ACTUAL_START_DATE,
            WO.ACTUAL_START_HOUR,
            WO.ACTUAL_START_MINUTE,
            WO.CREATED_DATE,
            WO.CREATED_BY,
            WO.MODIFIED_DATE,
            WO.MODIFIED_BY
        FROM
            WO
        WHERE
            (
                WO.CREATED_BY = 'TRAXIFACE'
                OR WO.PROJECT = 'EMS'
            )
            AND WO.CREATED_DATE >= to_date('{start_date}', 'yyyy-MM-dd')
            AND WO.CREATED_DATE <= to_date('{end_date}', 'yyyy-MM-dd')
    ),
    WO_INFO AS (
        SELECT
            WO.WO,
            WO.AC,
            CASE
                WHEN WO.PROJECT = 'EMS'
                AND WO.EXTERNAL_REFERENCE IS NULL THEN concat(WO.WO, '_', WO.AC)
                ELSE trim(substring(WO.EXTERNAL_REFERENCE, 1, 10))
            END AS EVENT_ID,
            CASE
                WHEN WO.PROJECT = 'EMS' THEN 'EMS'
                ELSE (
                    CASE
                        WHEN WO.WO_CATEGORY = 'HMV'
                        AND WO.PRIORITY = 'LOW' THEN 'HML'
                        ELSE (
                            CASE
                                WHEN WO.WO_CATEGORY IN ('OOS', 'PRK') THEN WO.WO_CATEGORY
                            END
                        )
                    END
                )
            END AS EVENT_TYPE,
            CASE
                WHEN WO.STATUS IN ('OPEN', 'COMPLETED') THEN concat(date_format(WO.SCHEDULE_ORG_COMPLETION_DATE, 'yyyy-MM-dd'), ' ', trim(
                    substring(
                        concat(lpad(cast(WO.SCHEDULE_ORG_COMPLETION_HOUR as string), 2, '0'), ':', lpad(cast(WO.SCHEDULE_ORG_COMPLETION_MINUTE as string), 2, '0')),
                        1,
                        8
                    )
                ))
                ELSE concat(date_format(WO.SCHEDULE_COMPLETION_DATE, 'yyyy-MM-dd'), ' ', trim(
                    substring(
                        concat(lpad(cast(WO.SCHEDULE_COMPLETION_HOUR as string), 2, '0'), ':', lpad(cast(WO.SCHEDULE_COMPLETION_MINUTE as string), 2, '0')),
                        1,
                        8
                    )
                ))
            END AS ACTUAL_COMPLETION_DATETIME,
            WO.SCHEDULE_START_DATE,
            concat(date_format(WO.ACTUAL_START_DATE, 'yyyy-MM-dd'), ' ', trim(
                substring(
                    concat(lpad(cast(WO.ACTUAL_START_HOUR as string), 2, '0'), ':', lpad(cast(WO.ACTUAL_START_MINUTE as string), 2, '0')),
                    1,
                    8
                )
            )) AS ACTUAL_START_DATETIME
        FROM
            WO_INIT WO
    ),
    TASK_COUNTS AS (
        SELECT
            WOTC.WO,
            COUNT(
                CASE
                    WHEN coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS TOTAL_TC,
            COUNT(
                CASE
                    WHEN WOTC.STATUS = 'CANCEL'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS TOTAL_CANCEL_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS TOTAL_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'OPEN'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS OPEN_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'CLOSED'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS CLOSED_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD NOT LIKE 'NR-%'
                    AND WOTC.STATUS = 'CANCEL'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS CANCEL_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS TOTAL_NON_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'OPEN'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS OPEN_NON_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'CLOSED'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS CLOSED_NON_ROUTINE_TC,
            COUNT(
                CASE
                    WHEN WOTC.TASK_CARD LIKE 'NR-%'
                    AND WOTC.STATUS = 'CANCEL'
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN WOTC.TASK_CARD
                END
            ) AS CANCEL_NON_ROUTINE_TC,
            SUM(
                CASE
                    WHEN WOTC.STATUS IN ('CLOSED', 'CANCEL')
                    AND coalesce(WOTC.TYPE, 'N/A') <> 'CA' THEN 1
                    ELSE 0
                END
            ) AS COMPLETED_OR_CANCELED_TC
        FROM
            WO_TASK_CARD WOTC
        WHERE
            WOTC.WO IN (
                SELECT
                    WO
                FROM
                    WO_INFO
            )
        GROUP BY
            WOTC.WO
    ),
    MAN_HOURS AS (
        SELECT
            WOTC.WO,
            coalesce(
                SUM(
                    CASE
                        WHEN VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                    END
                ),
                0
            ) AS TOTAL_MH,
            coalesce(
                SUM(
                    CASE
                        WHEN WOTC.STATUS <> 'OPEN'
                        AND VCT.MAN_HOURS <> 0 THEN VCT.MAN_HOURS
                    END
                ),
                0
            ) AS TOTAL_MH_COMPLETED
        FROM
            WO_TASK_CARD WOTC
            INNER JOIN VENDOR_CONTRACT_TASK VCT ON WOTC.TASK_CARD = VCT.TASK_CARD
        WHERE
            WOTC.WO IN (
                SELECT
                    WO
                FROM
                    WO_INFO
            )
        GROUP BY
            WOTC.WO
    ),
    LATEST_FLIGHT AS (
        SELECT
            AF.AC,
            AF.FLIGHT,
            AF.ORIGIN,
            AF.DESTINATION,
            AF.FLIGHT_DATE,
            AF.ON_HOUR,
            AF.ON_MINUTE
        FROM
            AC_ACTUAL_FLIGHTS AF
        WHERE
            (AF.AC, AF.CREATED_DATE) IN (
                SELECT
                    AC,
                    MAX(CREATED_DATE)
                FROM
                    AC_ACTUAL_FLIGHTS
                WHERE
                    AC IN (
                        SELECT
                            AC
                        FROM
                            WO_INFO
                    )
                GROUP BY
                    AC
            )
    )
    SELECT
        WO_INFO.WO,
        WO_INFO.AC,
        WO_INIT.AC_TYPE,
        WO_INFO.EVENT_ID,
        WO_INIT.LOCATION,
        WO_INIT.WO_DESCRIPTION,
        WO_INIT.VENDOR,
        WO_INIT.SITE,
        WO_INIT.PROJECT,
        WO_INIT.WO_CATEGORY,
        WO_INIT.PRIORITY,
        WO_INIT.EXTERNAL_REFERENCE,
        WO_INIT.STATUS,
        (unix_timestamp(to_timestamp(WO_INFO.ACTUAL_COMPLETION_DATETIME, 'yyyy-MM-dd HH:mm')) - 
         unix_timestamp(to_timestamp(WO_INFO.ACTUAL_START_DATETIME, 'yyyy-MM-dd HH:mm'))) / 86400.0 AS DURATION,
        CASE
            WHEN TC.TOTAL_TC = 0
            OR TC.COMPLETED_OR_CANCELED_TC = 0 THEN 0
            WHEN WO_INFO.SCHEDULE_START_DATE > current_date() THEN 0
            ELSE round(TC.COMPLETED_OR_CANCELED_TC / TC.TOTAL_TC * 100)
        END AS PERCENT_COMPLETED,
        TC.TOTAL_TC,
        TC.TOTAL_CANCEL_TC,
        TC.TOTAL_ROUTINE_TC,
        TC.OPEN_ROUTINE_TC,
        TC.CLOSED_ROUTINE_TC,
        TC.CANCEL_ROUTINE_TC,
        TC.TOTAL_NON_ROUTINE_TC,
        TC.OPEN_NON_ROUTINE_TC,
        TC.CLOSED_NON_ROUTINE_TC,
        TC.CANCEL_NON_ROUTINE_TC,
        MH.TOTAL_MH,
        MH.TOTAL_MH_COMPLETED,
        CASE
            WHEN MH.TOTAL_MH = 0 THEN 0
            ELSE round(MH.TOTAL_MH_COMPLETED / MH.TOTAL_MH * 100)
        END AS MH_ROUTINE_CHECK_COMPLETION,
        AC.AC_TYPE AS TYPE,
        AC.AC_SN,
        AC.AC_FLIGHT_HOURS,
        AC.AC_FLIGHT_MINUTES,
        AC.AC_CYCLES,
        AC.LAST_AC_REGISTRATION,
        substring(AC.BASIC_NUMBER, 1, 2) AS OPERATOR,
        LF.FLIGHT,
        LF.ORIGIN,
        LF.DESTINATION,
        LF.FLIGHT_DATE,
        LF.ON_HOUR,
        LF.ON_MINUTE,
        WO_INIT.SCHEDULE_START_DATE,
        WO_INIT.SCHEDULE_START_HOUR,
        WO_INIT.SCHEDULE_START_MINUTE,
        WO_INIT.SCHEDULE_COMPLETION_DATE,
        WO_INIT.SCHEDULE_COMPLETION_HOUR,
        WO_INIT.SCHEDULE_COMPLETION_MINUTE,
        WO_INIT.SCHEDULE_ORG_COMPLETION_DATE,
        WO_INIT.SCHEDULE_ORG_COMPLETION_HOUR,
        WO_INIT.SCHEDULE_ORG_COMPLETION_MINUTE,
        WO_INIT.ACTUAL_START_DATE,
        WO_INIT.ACTUAL_START_HOUR,
        WO_INIT.ACTUAL_START_MINUTE,
        WO_INIT.CREATED_DATE,
        WO_INIT.CREATED_BY,
        WO_INIT.MODIFIED_DATE,
        WO_INIT.MODIFIED_BY,
        LM.TIME_ZONE_NAME
    FROM
        WO_INFO WO_INFO
        LEFT JOIN WO_INIT WO_INIT ON WO_INFO.WO = WO_INIT.WO
        LEFT JOIN TASK_COUNTS TC ON WO_INFO.WO = TC.WO
        LEFT JOIN MAN_HOURS MH ON WO_INFO.WO = MH.WO
        LEFT JOIN AC_MASTER AC ON WO_INFO.AC = AC.AC
        LEFT JOIN LATEST_FLIGHT LF ON WO_INFO.AC = LF.AC
        LEFT JOIN LOCATION_MASTER LM ON LM.LOCATION = WO_INIT.LOCATION
    ORDER BY
        WO_INFO.SCHEDULE_START_DATE
    """

    logger.info(f"Started executing WO query for {start_date} to {end_date}")
    wo_finalDf = glue_wrapper.execute_spark_sql(wo_sql_query)
    wo_finalDf.cache()

    if not wo_finalDf.isEmpty():
        put_file_to_s3(wo_finalDf, DISTRIBUTION_FOLDER_PATH_WO, FILE_PREFIX_WO, start_date, end_date)
        logger.info(f"WO data written to S3 successfully for {start_date} to {end_date}")
    else:
        logger.info('No WO records to write')

    # Process PO data
    po_sql_query = f"""
    SELECT
        WO.WO,
        WO.EXTERNAL_REFERENCE,
        VC.CURRENCY AS CURRENCY_CODE,
        RV.RELATION_CODE AS SUPPLIER_CODE,
        RV.MAIL_ADDRESS_1 AS SUPPLIER_ADDRESS1,
        RV.MAIL_ADDRESS_2 AS SUPPLIER_ADDRESS2,
        RV.MAIL_CITY AS SUPPLIER_CITY,
        RV.MAIL_POST AS SUPPLIER_POST,
        RV.MAIL_STATE AS SUPPLIER_STATE,
        RV.MAIL_COUNTRY AS SUPPLIER_COUNTRY,
        RV.MAIL_PHONE AS SUPPLIER_PHONE,
        RV.MAIL_FAX AS SUPPLIER_FAX,
        RV.MAIL_CELL AS SUPPLIER_CELL,
        RV.MAIL_EMAIL AS SUPPLIER_EMAIL,
        SUBSTR(COALESCE(RV.NAME, ' - NO SUPPLIER - '), 1, 40) AS SUPPLIER
    FROM WO
    LEFT JOIN WO_VENDOR_CONTRACT WVC ON WO.WO = WVC.WO
    LEFT JOIN VENDOR_CONTRACT VC ON WO.VENDOR = VC.VENDOR 
        AND WO.LOCATION = VC.LOCATION 
        AND WVC.CONTRACT_TYPE = VC.CONTRACT_TYPE
    LEFT JOIN RELATION_MASTER RV ON WO.VENDOR = RV.RELATION_CODE 
        AND COALESCE(RV.RELATION_TRANSACTION, 'VENDOR') = 'VENDOR'
    WHERE
        SUBSTR(WO.LOCATION, 4, 4) IN ('1','2','3','4','5','6','7','8','9')
        AND WO.STATUS IN ('COMPLETED', 'POSTCOMPLT', 'OPEN', 'CLOSED')
        AND (WO.CREATED_BY = 'TRAXIFACE' OR WO.PROJECT = 'EMS')
        AND WO.CREATED_DATE >= to_date('{start_date}', 'yyyy-MM-dd')
        AND WO.CREATED_DATE <= to_date('{end_date}', 'yyyy-MM-dd')
    """

    logger.info(f"Started executing PO query for {start_date} to {end_date}")
    po_finalDf = glue_wrapper.execute_spark_sql(po_sql_query)
    po_finalDf.cache()

    if not po_finalDf.isEmpty():
        put_file_to_s3(po_finalDf, DISTRIBUTION_FOLDER_PATH_PO, FILE_PREFIX_PO, start_date, end_date)
        logger.info(f"PO data written to S3 successfully for {start_date} to {end_date}")
    else:
        logger.info('No PO records to write')




if not "pytest" in sys.modules:
    job.init(args["JOB_NAME"], args)

    print(f"Job {JOB_NAME} initialized with args: {args}")



    try:
        jdbc_username, jdbc_password, jdbc_url = get_trax_db_credentials()
        logger.info(f"TRAX DB credentials retrieved successfully")

        df_location_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.LOCATION_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo_vendor_contract = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO_VENDOR_CONTRACT",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_vendor_contract = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.VENDOR_CONTRACT",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_relation_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.RELATION_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo_task_card = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO_TASK_CARD",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_ac_actual_flights = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.AC_ACTUAL_FLIGHTS",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_ac_master = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.AC_MASTER",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_vendor_contract_task = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.VENDOR_CONTRACT_TASK",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo = glue_wrapper.load_data(
            source_type="JDBC",
            use_catalog=False,
            connection_options={
                "url": jdbc_url,
                "user": jdbc_username,
                "password": jdbc_password,
                "dbtable": "ODB.WO",
                "disableUrlUpdate": "true", 
                "zeroDateTimeBehavior": "convertToNull"
            }
        )

        df_wo.createOrReplaceTempView("WO")
            

        # Create temp views for all tables
        df_location_master.createOrReplaceTempView("LOCATION_MASTER")
        df_wo_vendor_contract.createOrReplaceTempView("WO_VENDOR_CONTRACT")
        df_vendor_contract.createOrReplaceTempView("VENDOR_CONTRACT")
        df_relation_master.createOrReplaceTempView("RELATION_MASTER")
        df_wo_task_card.createOrReplaceTempView("WO_TASK_CARD")
        df_ac_actual_flights.createOrReplaceTempView("AC_ACTUAL_FLIGHTS")
        df_ac_master.createOrReplaceTempView("AC_MASTER")
        df_vendor_contract_task.createOrReplaceTempView("VENDOR_CONTRACT_TASK")

        get_vendor_data(jdbc_url, jdbc_username, jdbc_password)

        yearly_date_pairs = get_yearly_date_pairs(START_DATE, END_DATE)
        for date_pair in yearly_date_pairs:
            get_wo_and_po_data(date_pair['start_date'], date_pair['end_date'], jdbc_url, jdbc_username, jdbc_password)

        logger.info(f"Job {JOB_NAME} completed successfully")
      
        


    except Exception as e:
        logger.error(f"Exception Occured in {JOB_NAME} : " + str(e))
        attemptCount = 0
        if 'attempt' in JOB_RUN_ID: 
            attemptCount = JOB_RUN_ID.split('attempt_')[-1]
        
        if attemptCount == '2':
            send_error_to_sns(e)
        raise e
    
    glue_wrapper.commit()

if __name__ == "__main__":
    pass