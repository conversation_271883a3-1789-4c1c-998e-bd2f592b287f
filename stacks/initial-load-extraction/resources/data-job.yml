name: ${self:custom.base}-data-job
scriptPath: scripts/data-job.py
type: spark
glueVersion: python3-4.0
tempDir: true
role: ${cf:ac-odh-common-resources-glue-${self:provider.stage}.ConsumptionGlueJobCommonRoleArn}
MaxConcurrentRuns: ${self:custom.params.GLUE_JOB_CONFIG_MAXCONCURRENTRUNS}
WorkerType: ${self:custom.params.GLUE_JOB_CONFIG_WORKERTYPE}
NumberOfWorkers: ${self:custom.params.GLUE_JOB_CONFIG_NUMBEROFWORKERS}
Timeout: ${self:custom.params.GLUE_JOB_CONFIG_TIMEOUT}
MaxRetries: ${self:custom.params.GLUE_JOB_CONFIG_MAXRETRIES}
Connections:
  - ${cf:ac-odh-common-resources-glue-${self:provider.stage}.ConnectionNameODSMaintenanceOperationsDatastoreReaderProxy}
DefaultArguments:
  enableContinuousCloudwatchLog: ${self:custom.params.ENABLE_GLUE_CLOUDWATCH_LOGS}
  enableAutoScaling: true
  enableMetrics: true
  enableObservabilityMetrics: "true"
  enableSparkUi: "true"
  sparkEventLogsPath: ${self:custom.params.GLUE_SPARK_UI_LOGS_PATH}
  extraPyFiles: ${self:custom.params.GLUE_UTILS_PATH}
  customArguments:
    "--DISTRIBUTION_BUCKET_NAME": ${self:custom.params.DISTRIBUTION_BUCKET_NAME}
    "--REGION": ${self:provider.region}
    "--ACCOUNT_ID": !Sub ${AWS::AccountId}
    "--TRAX_DB_SECRET": ${self:custom.params.TRAX_DB_SECRET}
    "--START_DATE": ${self:custom.params.START_DATE}
    "--END_DATE": ${self:custom.params.END_DATE}
    "--DISTRIBUTION_FOLDER_PATH_WO": ${self:custom.params.WO_GENERATING_FILE_PATH}
    "--DISTRIBUTION_FOLDER_PATH_VENDOR": ${self:custom.params.VENDOR_GENERATING_FILE_PATH}
    "--DISTRIBUTION_FOLDER_PATH_PO": ${self:custom.params.PO_GENERATING_FILE_PATH}
    "--FILE_PREFIX_WO": ${self:custom.params.FILE_PREFIX_WO}
    "--FILE_PREFIX_VENDOR": ${self:custom.params.FILE_PREFIX_VENDOR}
    "--FILE_PREFIX_PO": ${self:custom.params.FILE_PREFIX_PO}
    "--SNS_TOPIC_ARN": ${cf:ac-odh-batch-distribution-webfocus-common-resources-${self:provider.stage}.ErrorNotifyTopicArn}
    "--PENDING_FILE_GENERATING_PATH": ${self:custom.params.PENDING_FILE_GENERATING_PATH}
Tags:
  aws-service: 'AWS Glue'
  unique-id: ${self:custom.base}-data-job
