import "source-map-support/register";
import * as cdk from "aws-cdk-lib";
import { getConfig } from "./stacks/share-resources/src/utils/config";
import { sanitizeTags } from "./utils/tag-utils";
import { createSynthesizer, SynthesizerSSMConfig } from "./synthesizer/create-synthesizer";
import { OAuthRefreshStack } from "./stacks/share-resources/shared-resources-stack";
import { OpenSearchStack } from "./stacks/opensearch/opensearch-stack";
import { applyTagsToStacks } from "./utils/cdk-tagging";

async function main() {
  const app = new cdk.App();
  const environment = app.node.tryGetContext("stage") || "int-cac1";
  const config = getConfig(environment);
  const stackName = config.stackName;

  const sanitizedTags = sanitizeTags(config.tags);

  const synthesizerConfig: SynthesizerSSMConfig = config.defaultStackSynthesizer;
  const synthesizer = await createSynthesizer(synthesizerConfig);

  const oAuthRefresh = new OAuthRefreshStack(app, stackName, {
    env: { account: config.account, region: config.region },
    config,
    synthesizer,
    terminationProtection: environment === "prodca1",
  });

  const openSearchStack = new OpenSearchStack(app, `${stackName}-opensearch`, {
    env: { account: config.account, region: config.region },
    config,
    synthesizer,
  });

  applyTagsToStacks([oAuthRefresh, openSearchStack], sanitizedTags);

  app.synth();
}

main().catch((err) => {
  console.error("CDK App failed:", err);
  process.exit(1);
});