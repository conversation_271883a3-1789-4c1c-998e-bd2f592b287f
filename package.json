{"name": "ac-odh-batch-distribution-webfocus", "version": "1.0.0", "description": "This repo will get data from Trax and generate a CSV file for the web focus", "bin": {"app": "app.ts"}, "repository": {"type": "git", "url": "git+https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus/issues"}, "homepage": "https://github.com/AC-IT-Development/ac-odh-batch-distribution-webfocus#readme", "dependencies": {"@aws-sdk/client-dynamodb": "^3.774.0", "@aws-sdk/client-secrets-manager": "^3.726.0", "aws-cdk-lib": "^2.172.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21", "dbaas-logger": "git+ssh://**************/AC-IT-Development/dbaas-logger.git#vstable_4.0.7", "ac-utils": "git+ssh://**************/AC-IT-Development/ac-utils.git#2.0.0", "oracledb": "^6.8.0", "knex": "3.1.0", "fast-csv": "^5.0.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/aws-lambda": "^8.10.146", "@types/jest": "^29.5.14", "@types/node": "^20.9.0", "aws-cdk": "^2.172.0", "aws-sdk-client-mock": "^4.1.0", "aws-sdk-client-mock-jest": "^4.1.0", "esbuild": "^0.24.2", "esbuild-plugin-tsc": "^0.4.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "tsx": "^4.19.2", "typescript": "~5.2.2", "@aws-sdk/client-ssm": "^3.540.0"}}