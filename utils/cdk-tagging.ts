import { Tags, Stack } from "aws-cdk-lib";

/**
 * Applies sanitized tags to the given CDK stack or construct.
 * Useful for applying global or environment-wide tags.
 *
 * @param resource - CDK stack or construct
 * @param tags - Record of tag key/value pairs
 */
export function applyTags(resource: Stack, tags: Record<string, string>): void {
  for (const [key, value] of Object.entries(tags)) {
    Tags.of(resource).add(key, value);
  }
}

/**
 * Applies tags to multiple stacks at once.
 *
 * @param stacks - Array of CDK stacks or constructs
 * @param tags - Record of tag key/value pairs
 */
export function applyTagsToStacks(stacks: Stack[], tags: Record<string, string>): void {
  stacks.forEach((stack) => applyTags(stack, tags));
}