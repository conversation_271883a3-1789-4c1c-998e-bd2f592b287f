/**
 * Removes unsafe characters from a tag value.
 * Ensures compatibility with AWS tag restrictions.
 */
export function sanitizeTagValue(value: string): string {
  return value.replace(/[^a-zA-Z0-9\s._:/=+\-@]/g, "").trim();
}

/**
 * Sanitizes all tag values in the given tag object.
 * Example:
 *   Input:  { Project: "My@App!", Owner: " <EMAIL> " }
 *   Output: { Project: "MyApp", Owner: "<EMAIL>" }
 */
export function sanitizeTags(tags: Record<string, string>): Record<string, string> {
  const sanitized: Record<string, string> = {};
  for (const [key, value] of Object.entries(tags)) {
    sanitized[key] = sanitizeTagValue(value);
  }
  return sanitized;
}